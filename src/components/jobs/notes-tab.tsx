'use client';

import { useState, useMemo } from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';

import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Save, X, StickyNote, Trash2, Clock } from 'lucide-react';
import { format } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import type { ApplicationLogEvent } from "@/lib/types";

// 笔记事件类型
interface NoteEvent {
  type: 'note_add' | 'note_remove';
  id: string;
  note?: string;
  created_at: string;
}

// 笔记Tab属性
interface NotesTabProps {
  applicationLog: ApplicationLogEvent[];
  onNoteAdd?: (note: string, createdAt?: string) => Promise<void> | void;
  onNoteRemove?: (noteId: string, createdAt?: string) => Promise<void> | void;
}

export function NotesTab({ 
  applicationLog, 
  onNoteAdd, 
  onNoteRemove 
}: NotesTabProps) {
  const [noteContent, setNoteContent] = useState('');
  const [customDateTime, setCustomDateTime] = useState<Date | null>(null);
  const [tempDateTime, setTempDateTime] = useState<Date>(new Date());
  const [showTimeSelector, setShowTimeSelector] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  // 从日志中提取有效笔记（按照add/remove事件计算最终状态）
  const activeNotes = useMemo(() => {
    return getActiveNotesFromLog(applicationLog);
  }, [applicationLog]);

  // 生成自定义时间字符串
  const getCustomTimeString = () => {
    if (!customDateTime) return undefined;
    return customDateTime.toISOString();
  };

  // 处理保存笔记
  const handleSaveNote = async () => {
    if (!noteContent.trim() || !onNoteAdd) {
      return;
    }

    try {
      setIsSaving(true);
      const createdAt = getCustomTimeString();
      await onNoteAdd(noteContent.trim(), createdAt);
      
      // 重置表单
      setNoteContent('');
      setCustomDateTime(null);
      setShowTimeSelector(false);
    } catch (error) {
      console.error('保存笔记失败:', error);
    } finally {
      setIsSaving(false);
    }
  };

  // 处理取消编辑
  const handleCancel = () => {
    setNoteContent('');
    setCustomDateTime(null);
    setShowTimeSelector(false);
  };

  // 处理删除笔记
  const handleDeleteNote = async (noteId: string) => {
    if (!onNoteRemove) {
      if (process.env.NODE_ENV !== 'production') {
        console.log('删除笔记功能暂未实现');
      }
      return;
    }

    const confirmed = window.confirm('确定要删除这条笔记吗？');
    if (!confirmed) return;

    try {
      await onNoteRemove(noteId);
    } catch (error) {
      console.error('删除笔记失败:', error);
    }
  };

  // 格式化时间显示
  const formatTime = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return format(date, 'yyyy年MM月dd日 HH:mm', { locale: zhCN });
    } catch {
      return dateString;
    }
  };

  // 格式化当前时间显示
  const formatCurrentTime = () => {
    const now = new Date();
    return format(now, 'MM月dd日 HH:mm', { locale: zhCN });
  };

  // 格式化自定义时间显示
  const formatCustomDateTime = () => {
    if (!customDateTime) return formatCurrentTime();
    return format(customDateTime, 'MM月dd日 HH:mm', { locale: zhCN });
  };

  // 处理时间选择器确认
  const handleTimeConfirm = () => {
    setCustomDateTime(tempDateTime);
    setShowTimeSelector(false);
  };

  // 处理时间选择器取消
  const handleTimeCancel = () => {
    setTempDateTime(customDateTime || new Date());
    setShowTimeSelector(false);
  };

  // 生成年份选项（当前年份前后3年）
  const currentYear = new Date().getFullYear();
  const yearOptions = Array.from({ length: 7 }, (_, i) => currentYear - 3 + i);

  // 生成月份选项
  const monthOptions = Array.from({ length: 12 }, (_, i) => i + 1);

  // 生成日期选项（根据选中的年月）
  const getDaysInMonth = (year: number, month: number) => {
    return new Date(year, month, 0).getDate();
  };

  const dayOptions = Array.from(
    { length: getDaysInMonth(tempDateTime.getFullYear(), tempDateTime.getMonth() + 1) }, 
    (_, i) => i + 1
  );

  // 生成小时选项
  const hourOptions = Array.from({ length: 24 }, (_, i) => i);

  // 生成分钟选项（5分钟间隔）
  const minuteOptions = Array.from({ length: 12 }, (_, i) => i * 5);

  return (
    <div className="p-6 space-y-6">
      {/* 顶部：笔记编辑器 */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium text-black">添加新笔记</h3>
          
          {/* 自定义时间选择器 */}
          <Popover open={showTimeSelector} onOpenChange={setShowTimeSelector}>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                size="sm"
                className="text-gray-600 border-gray-300"
                onClick={() => setTempDateTime(customDateTime || new Date())}
              >
                <Clock className="w-4 h-4 mr-2" />
                {formatCustomDateTime()}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-80 p-0" align="end">
              <div className="p-4 space-y-4">
                <div className="text-center">
                  <Label className="text-sm font-medium text-black">选择时间</Label>
                </div>
                
                <div className="grid grid-cols-2 gap-3">
                  {/* 年份 */}
                  <div className="space-y-2">
                    <Label className="text-xs text-gray-600">年</Label>
                    <Select 
                      value={tempDateTime.getFullYear().toString()} 
                      onValueChange={(value) => {
                        const newDate = new Date(tempDateTime);
                        newDate.setFullYear(parseInt(value));
                        setTempDateTime(newDate);
                      }}
                    >
                      <SelectTrigger className="h-8">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {yearOptions.map((year) => (
                          <SelectItem key={year} value={year.toString()}>
                            {year}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  
                  {/* 月份 */}
                  <div className="space-y-2">
                    <Label className="text-xs text-gray-600">月</Label>
                    <Select 
                      value={(tempDateTime.getMonth() + 1).toString()}
                      onValueChange={(value) => {
                        const newDate = new Date(tempDateTime);
                        newDate.setMonth(parseInt(value) - 1);
                        setTempDateTime(newDate);
                      }}
                    >
                      <SelectTrigger className="h-8">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {monthOptions.map((month) => (
                          <SelectItem key={month} value={month.toString()}>
                            {month}月
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                
                <div className="grid grid-cols-3 gap-3">
                  {/* 日期 */}
                  <div className="space-y-2">
                    <Label className="text-xs text-gray-600">日</Label>
                    <Select 
                      value={tempDateTime.getDate().toString()}
                      onValueChange={(value) => {
                        const newDate = new Date(tempDateTime);
                        newDate.setDate(parseInt(value));
                        setTempDateTime(newDate);
                      }}
                    >
                      <SelectTrigger className="h-8">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {dayOptions.map((day) => (
                          <SelectItem key={day} value={day.toString()}>
                            {day}日
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  
                  {/* 小时 */}
                  <div className="space-y-2">
                    <Label className="text-xs text-gray-600">时</Label>
                    <Select 
                      value={tempDateTime.getHours().toString()}
                      onValueChange={(value) => {
                        const newDate = new Date(tempDateTime);
                        newDate.setHours(parseInt(value));
                        setTempDateTime(newDate);
                      }}
                    >
                      <SelectTrigger className="h-8">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {hourOptions.map((hour) => (
                          <SelectItem key={hour} value={hour.toString()}>
                            {hour.toString().padStart(2, '0')}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  
                  {/* 分钟 */}
                  <div className="space-y-2">
                    <Label className="text-xs text-gray-600">分</Label>
                    <Select 
                      value={tempDateTime.getMinutes().toString()}
                      onValueChange={(value) => {
                        const newDate = new Date(tempDateTime);
                        newDate.setMinutes(parseInt(value));
                        setTempDateTime(newDate);
                      }}
                    >
                      <SelectTrigger className="h-8">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {minuteOptions.map((minute) => (
                          <SelectItem key={minute} value={minute.toString()}>
                            {minute.toString().padStart(2, '0')}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                
                <div className="flex gap-2 pt-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleTimeCancel}
                    className="flex-1"
                  >
                    取消
                  </Button>
                  <Button
                    size="sm"
                    onClick={handleTimeConfirm}
                    className="flex-1"
                  >
                    确认
                  </Button>
                </div>
              </div>
            </PopoverContent>
          </Popover>
        </div>

        {/* 笔记内容输入 */}
        <Textarea
          value={noteContent}
          onChange={(e) => setNoteContent(e.target.value)}
          placeholder="输入您的笔记内容..."
          rows={4}
          className="bg-white border-gray-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20"
        />

        {/* 操作按钮 */}
        <div className="flex items-center gap-2">
          <Button
            onClick={handleSaveNote}
            disabled={!noteContent.trim() || isSaving}
            className="bg-blue-500 hover:bg-blue-600 text-white focus:ring-2 focus:ring-blue-500/20"
          >
            <Save className="w-4 h-4 mr-2" />
            {isSaving ? '保存中...' : '保存笔记'}
          </Button>
          
          <Button
            variant="outline"
            onClick={handleCancel}
            disabled={!noteContent && !customDateTime}
            className="text-gray-600 border-gray-300"
          >
            <X className="w-4 h-4 mr-2" />
            取消
          </Button>
        </div>
      </div>

      <Separator className="bg-gray-200" />

      {/* 笔记记录列表 */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium text-black">笔记记录</h3>
          <Badge variant="secondary" className="text-xs">
            {activeNotes.length} 条笔记
          </Badge>
        </div>

        {activeNotes.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <StickyNote className="w-8 h-8 mx-auto mb-2 opacity-50" />
            <div>暂无笔记记录</div>
            <p className="text-sm mt-1">在上方添加您的第一条笔记</p>
          </div>
        ) : (
          <div className="space-y-4">
            {activeNotes.map((noteEvent) => (
              <div
                key={noteEvent.id}
                className="bg-gray-50 border border-gray-200 rounded-lg p-4 space-y-3"
              >
                {/* 笔记头部：时间和操作 */}
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <StickyNote className="w-4 h-4" />
                    <span>{formatTime(noteEvent.created_at)}</span>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleDeleteNote(noteEvent.id)}
                    className="h-6 px-2 text-gray-400 hover:text-red-500"
                  >
                    <Trash2 className="w-3 h-3" />
                  </Button>
                </div>

                {/* 笔记内容 */}
                <div className="text-sm text-black leading-relaxed">
                  {noteEvent.note}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}

// 纯函数：从应用日志中计算当前有效的笔记集合
export function getActiveNotesFromLog(
  applicationLog: ApplicationLogEvent[]
): NoteEvent[] {
  const noteMap = new Map<string, NoteEvent>();

  // 按时间顺序处理日志事件
  const sortedLog = [...applicationLog].sort((a, b) => {
    const timeA = new Date(a.created_at as string).getTime();
    const timeB = new Date(b.created_at as string).getTime();
    return timeA - timeB; // 正序，先处理早期事件
  });

  for (const event of sortedLog) {
    if (event.type === 'note_add' && event.id && event.note) {
      // 添加笔记事件
      noteMap.set(event.id as string, {
        type: 'note_add',
        id: event.id as string,
        note: event.note as string,
        created_at: event.created_at as string,
      });
    } else if (event.type === 'note_remove' && event.id) {
      // 删除笔记事件（软删除：从map中移除）
      noteMap.delete(event.id as string);
    }
  }

  // 返回按时间倒序排列的有效笔记
  const result = Array.from(noteMap.values());
  return result.sort((a, b) => {
    const timeA = new Date(a.created_at).getTime();
    const timeB = new Date(b.created_at).getTime();
    return timeB - timeA; // 倒序，最新的在前
  });
}
