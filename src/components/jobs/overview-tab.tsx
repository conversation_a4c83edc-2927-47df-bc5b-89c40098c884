'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { Save } from 'lucide-react';
import { Timeline } from './timeline';
import type { Job, ApplicationLogEvent } from "@/lib/types";



interface OverviewTabProps {
  job: Job;
  onSave?: (updatedFields: Partial<Job>) => Promise<{ success: boolean; error?: string }> | Promise<void>;
  onEventTimeUpdate?: (matcher: Partial<ApplicationLogEvent>, newTime: string) => Promise<void> | void;
}

export function OverviewTab({ job, onSave, onEventTimeUpdate }: OverviewTabProps) {
  const [formData, setFormData] = useState({
    position_title: job.position_title || '',
    company_name: job.company_name || '',
    location: job.location || '',
    employment_type: job.employment_type || null,
    work_location: job.work_location || null,
    job_url: job.job_url || '',
    salary_range: job.salary_range || '',
    description: job.description || '',
  });

  const [isSaving, setIsSaving] = useState(false);

  // 当job ID变化时重置表单数据（避免切换岗位时数据混乱）
  useEffect(() => {
    setFormData({
      position_title: job.position_title || '',
      company_name: job.company_name || '',
      location: job.location || '',
      employment_type: job.employment_type || null,
      work_location: job.work_location || null,
      job_url: job.job_url || '',
      salary_range: job.salary_range || '',
      description: job.description || '',
    });
  }, [job.id]); // 只依赖job.id，避免无限循环

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSave = async () => {
    if (!onSave) {
      if (process.env.NODE_ENV !== 'production') {
        console.log('保存功能暂未实现:', formData);
      }
      return;
    }

    try {
      setIsSaving(true);
      await onSave(formData);
      // Toast反馈和数据同步都在JobListingsCard中处理
    } catch (error) {
      console.error('保存失败:', error);
      // 错误处理在JobListingsCard中通过Toast完成
    } finally {
      setIsSaving(false);
    }
  };

  const hasChanges = Object.entries(formData).some(([key, value]) => {
    const originalValue = job[key as keyof Job] || '';
    return value !== originalValue;
  });

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 p-6">
      {/* 左侧：岗位字段表单 */}
      <div className="space-y-6">
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-black">岗位信息</h3>
          
          <div className="grid grid-cols-1 gap-4">
            {/* 职位名称 */}
            <div className="space-y-2">
              <Label htmlFor="position_title" className="text-sm font-medium text-black">
                职位名称 *
              </Label>
              <Input
                id="position_title"
                value={formData.position_title}
                onChange={(e) => handleInputChange('position_title', e.target.value)}
                placeholder="请输入职位名称"
                className="bg-white border-gray-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20"
              />
            </div>

            {/* 公司名称 */}
            <div className="space-y-2">
              <Label htmlFor="company_name" className="text-sm font-medium text-black">
                公司名称
              </Label>
              <Input
                id="company_name"
                value={formData.company_name}
                onChange={(e) => handleInputChange('company_name', e.target.value)}
                placeholder="请输入公司名称"
                className="bg-white border-gray-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20"
              />
            </div>

            {/* 工作地点 */}
            <div className="space-y-2">
              <Label htmlFor="location" className="text-sm font-medium text-black">
                工作地点
              </Label>
              <Input
                id="location"
                value={formData.location}
                onChange={(e) => handleInputChange('location', e.target.value)}
                placeholder="请输入工作地点"
                className="bg-white border-gray-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20"
              />
            </div>

            {/* 职位类型与办公方式 */}
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label className="text-sm font-medium text-black">职位类型</Label>
                <Select
                  value={formData.employment_type || ''}
                  onValueChange={(value) => handleInputChange('employment_type', value)}
                >
                  <SelectTrigger className="bg-white border-gray-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20">
                    <SelectValue placeholder="选择职位类型" />
                  </SelectTrigger>
                  <SelectContent className="bg-white border-gray-200">
                    <SelectItem value="full_time">全职</SelectItem>
                    <SelectItem value="part_time">兼职</SelectItem>
                    <SelectItem value="co_op">Co-op</SelectItem>
                    <SelectItem value="internship">实习</SelectItem>
                    <SelectItem value="contract">合同工</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label className="text-sm font-medium text-black">办公方式</Label>
                <Select
                  value={formData.work_location || ''}
                  onValueChange={(value) => handleInputChange('work_location', value)}
                >
                  <SelectTrigger className="bg-white border-gray-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20">
                    <SelectValue placeholder="选择办公方式" />
                  </SelectTrigger>
                  <SelectContent className="bg-white border-gray-200">
                    <SelectItem value="remote">远程</SelectItem>
                    <SelectItem value="in_person">现场</SelectItem>
                    <SelectItem value="hybrid">混合</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* 岗位链接 */}
            <div className="space-y-2">
              <Label htmlFor="job_url" className="text-sm font-medium text-black">
                岗位链接
              </Label>
              <Input
                id="job_url"
                type="url"
                value={formData.job_url}
                onChange={(e) => handleInputChange('job_url', e.target.value)}
                placeholder="https://..."
                className="bg-white border-gray-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20"
              />
            </div>

            {/* 薪资范围 */}
            <div className="space-y-2">
              <Label htmlFor="salary_range" className="text-sm font-medium text-black">
                薪资范围
              </Label>
              <Input
                id="salary_range"
                value={formData.salary_range}
                onChange={(e) => handleInputChange('salary_range', e.target.value)}
                placeholder="例如：80k-120k CAD"
                className="bg-white border-gray-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20"
              />
            </div>

            {/* 岗位描述 */}
            <div className="space-y-2">
              <Label htmlFor="description" className="text-sm font-medium text-black">
                岗位描述
              </Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="请输入岗位描述"
                rows={4}
                className="bg-white border-gray-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20"
              />
            </div>
          </div>

          {/* 保存按钮 */}
          <Button
            onClick={handleSave}
            disabled={!hasChanges || isSaving}
            className="w-full bg-blue-500 hover:bg-blue-600 text-white focus:ring-2 focus:ring-blue-500/20"
          >
            <Save className="w-4 h-4 mr-2" />
            {isSaving ? '保存中...' : '保存更改'}
          </Button>
        </div>
      </div>

      {/* 右侧：时间轴 */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium text-black">申请时间轴</h3>
        </div>
        
        <Separator className="bg-gray-200" />
        
        <Timeline 
          applicationLog={job.application_log || []}
          onEventTimeUpdate={onEventTimeUpdate}
        />
      </div>
    </div>
  );
}
