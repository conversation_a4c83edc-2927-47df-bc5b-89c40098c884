'use client';

import { useState, useRef, useCallback, useEffect } from 'react';
import { Button } from '@/components/ui/button';

import { formatJobListing } from '@/utils/actions/jobs/ai';
import { createJob } from '@/utils/actions/jobs/actions';
import { toast } from 'sonner';
import { Loader2, Send } from 'lucide-react';
import { Job } from '@/lib/types';
import { cn } from '@/lib/utils';
import { designTokens } from '@/lib/design-tokens';
import { jobDescriptionInputSchema } from '@/lib/zod-schemas';
import { ZodError } from 'zod';

interface InlineJobComposerProps {
  onJobAdded?: (job: Job) => void;
}

/**
 * 内联岗位输入组件 - 固定在屏幕底部，支持自适应高度与三档响应式
 * 复用现有 formatJobListing + createJob 流程，遵循设计令牌规范
 */
export function InlineJobComposer({ onJobAdded }: InlineJobComposerProps) {
  const [value, setValue] = useState('');
  const [isFormatting, setIsFormatting] = useState(false);
  const [isCreating, setIsCreating] = useState(false);
  const [isInvalid, setIsInvalid] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string>('');
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // 初次渲染时聚焦到输入框
  useEffect(() => {
    // 稍微延迟聚焦，确保组件完全渲染
    const timer = setTimeout(() => {
      textareaRef.current?.focus();
    }, 100);
    
    return () => clearTimeout(timer);
  }, []);

  // 自适应高度调整
  const adjustTextareaHeight = useCallback(() => {
    const textarea = textareaRef.current;
    if (!textarea) return;

    // 重置高度以获取正确的 scrollHeight
    textarea.style.height = 'auto';
    
    // 计算新高度（上限 144px = 约 6 行）
    const newHeight = Math.min(textarea.scrollHeight, 144);
    textarea.style.height = `${newHeight}px`;
  }, []);

  // 校验岗位描述 - 使用统一的 zod schema
  const validateJobDescription = useCallback((text: string) => {
    try {
      jobDescriptionInputSchema.parse({ description: text });
      setIsInvalid(false);
      setErrorMessage('');
      return { isValid: true };
    } catch (error) {
      if (error instanceof ZodError) {
        const errorMsg = error.errors[0]?.message || '输入格式不正确';
        setIsInvalid(true);
        setErrorMessage(errorMsg);
        return { isValid: false, error: errorMsg };
      }
      const errorMsg = '校验失败';
      setIsInvalid(true);
      setErrorMessage(errorMsg);
      return { isValid: false, error: errorMsg };
    }
  }, []);

  // 处理输入变化
  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value;
    setValue(newValue);
    setIsInvalid(false);
    
    // 自适应高度
    requestAnimationFrame(adjustTextareaHeight);
  };

  // 处理键盘事件 - 增强无障碍支持
  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter') {
      if (!e.shiftKey) {
        e.preventDefault();
        handleSubmit();
      } else {
        // Shift+Enter 换行后调整高度
        requestAnimationFrame(adjustTextareaHeight);
      }
    } else if (e.key === 'Escape') {
      // Esc 键清空输入并移除焦点
      handleClear();
      textareaRef.current?.blur();
    }
  };

  // 处理滚动事件，防止与外部页面滚动冲突
  const handleWheel = (e: React.WheelEvent<HTMLTextAreaElement>) => {
    const textarea = e.currentTarget;
    const { scrollTop, scrollHeight, clientHeight } = textarea;
    
    // 如果内容高度小于等于容器高度，不需要滚动，阻止事件冒泡
    if (scrollHeight <= clientHeight) {
      e.preventDefault();
      return;
    }
    
    // 如果向上滚动且已经在顶部，阻止事件冒泡
    if (e.deltaY < 0 && scrollTop === 0) {
      e.preventDefault();
      return;
    }
    
    // 如果向下滚动且已经在底部，阻止事件冒泡
    if (e.deltaY > 0 && scrollTop >= scrollHeight - clientHeight) {
      e.preventDefault();
      return;
    }
    
    // 其他情况停止事件冒泡，让 textarea 内部处理滚动
    e.stopPropagation();
  };



  // 处理提交
  const handleSubmit = async () => {
    const validation = validateJobDescription(value);
    if (!validation.isValid) {
      setIsInvalid(true);
      toast.error('校验错误', {
        description: validation.error,
      });
      return;
    }

    try {
      setIsFormatting(true);

      // 从 localStorage 获取模型配置
      const MODEL_STORAGE_KEY = 'SnapOffer-default-model';
      const selectedModel = localStorage.getItem(MODEL_STORAGE_KEY);

      // 使用 AI 格式化岗位信息
      const formattedJob = await formatJobListing(value, {
        model: selectedModel || 'gpt-4.1-nano'
      });

      setIsFormatting(false);
      setIsCreating(true);

      // 创建岗位到数据库
      const newJob = await createJob(formattedJob);
      
      // 成功提示
      toast.success('成功', {
        description: '岗位添加成功',
      });

      // 通知父组件新岗位已添加
      onJobAdded?.(newJob);
      
      // 重置状态
      setValue('');
      setIsInvalid(false);
      if (textareaRef.current) {
        textareaRef.current.style.height = 'auto';
      }

    } catch (error) {
      // 仅在开发环境输出详细错误信息
      if (process.env.NODE_ENV === 'development') {
        console.error('Job creation failed:', error);
      }
      
      // 处理特定错误类型
      if (error instanceof Error) {
        if (error.message.toLowerCase().includes('api key') || 
            error.message.toLowerCase().includes('unauthorized') ||
            error.message.toLowerCase().includes('invalid key')) {
          toast.error('API 密钥错误', {
            description: '您的 API 密钥有问题。请检查您的设置并重试。',
          });
        } else {
          toast.error('创建失败', {
            description: error.message,
          });
        }
      } else {
        toast.error('创建失败', {
          description: '创建岗位失败，请重试',
        });
      }
    } finally {
      setIsFormatting(false);
      setIsCreating(false);
    }
  };

  const isLoading = isFormatting || isCreating;

  return (
    <>
      {/* 底部固定输入条 - ChatGPT 样式 */}
      <div className={cn(
        "fixed left-0 right-0",
        "bottom-[env(safe-area-inset-bottom)] mb-6"
      )} style={{ zIndex: designTokens.zIndex.fixed }}>
        <div className="max-w-3xl mx-auto px-4">
          {/* ChatGPT 风格输入框 */}
          <div className={cn(
            "relative",
            "bg-white",
            "border border-gray-300",
            "rounded-2xl",
            "shadow-lg",
            "h-[120px]",
            "overflow-hidden"
          )}>
            {/* 文本输入区域 - 填满整个容器 */}
            <div className="absolute inset-3">
              <label htmlFor="job-description" className="sr-only">
                岗位描述
              </label>
              <textarea
                ref={textareaRef}
                id="job-description"
                value={value}
                onChange={handleChange}
                onKeyDown={handleKeyDown}
                onWheel={handleWheel}
                placeholder="粘贴完整的岗位描述，AI 将自动解析并创建岗位申请"
                className={cn(
                  "w-full h-full bg-transparent",
                  "border-none outline-none resize-none",
                  "text-sm text-gray-900",
                  "placeholder:text-gray-400",
                  "overflow-y-auto",
                  "leading-5",
                  "py-0",
                  // 隐藏滚动条
                  "[&::-webkit-scrollbar]:hidden",
                  "[-ms-overflow-style:none]",
                  "[scrollbar-width:none]"
                )}
                disabled={isLoading}
                aria-invalid={isInvalid}
                aria-describedby={isInvalid ? "job-description-error" : undefined}
              />
            </div>

            {/* 发送按钮 - 右下角绝对定位，悬浮在文字上方 */}
            <div className="absolute bottom-2 right-2 z-10">
              <Button
                type="button"
                onClick={handleSubmit}
                disabled={isLoading || !value.trim()}
                className={cn(
                  "w-7 h-7 p-0",
                  "bg-black text-white",
                  "hover:bg-gray-800",
                  "disabled:bg-gray-200 disabled:text-gray-400",
                  "rounded-full",
                  "transition-all duration-200",
                  "flex items-center justify-center",
                  "flex-shrink-0",
                  "shadow-sm"
                )}
                aria-label="发送消息"
              >
                {isLoading ? (
                  <Loader2 className="w-3.5 h-3.5 animate-spin" />
                ) : (
                  <Send className="w-3.5 h-3.5" />
                )}
              </Button>
            </div>


          </div>
        </div>
      </div>

      {/* 错误提示区域 - 增强无障碍支持 */}
      {isInvalid && errorMessage && (
        <div 
          id="job-description-error" 
          className="sr-only" 
          aria-live="polite" 
          role="alert"
        >
          {errorMessage}
        </div>
      )}
    </>
  );
}
