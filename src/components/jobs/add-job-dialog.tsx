'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { JobDescriptionInput } from '@/components/resume/management/job-description-input';
import { formatJobListing } from '@/utils/actions/jobs/ai';
import { createJob } from '@/utils/actions/jobs/actions';
import { toast } from 'sonner';
import { Loader2, Plus } from 'lucide-react';
import { Job } from '@/lib/types';

interface AddJobDialogProps {
  onJobAdded?: (job: Job) => void;
}

/**
 * 添加岗位Dialog组件 - 复用现有AI解析流程
 * Task 10.1: 集成现有formatJobListing和createJob流程
 */
export function AddJobDialog({ onJobAdded }: AddJobDialogProps) {
  const [open, setOpen] = useState(false);
  const [jobDescription, setJobDescription] = useState('');
  const [isJobDescriptionInvalid, setIsJobDescriptionInvalid] = useState(false);
  const [isFormatting, setIsFormatting] = useState(false);
  const [isCreating, setIsCreating] = useState(false);

  // 验证职位描述
  const validateJobDescription = (value: string) => {
    const trimmed = value.trim();
    if (trimmed.length < 50) {
      return { jobDescription: '职位描述至少需要50个字符' };
    }
    if (trimmed.length > 10000) {
      return { jobDescription: '职位描述不能超过10000个字符' };
    }
    return {};
  };

  // 处理创建岗位 - 复用TailoredJobCard中的完整流程
  const handleCreateJob = async () => {
    const errors = validateJobDescription(jobDescription);
    if (Object.keys(errors).length > 0) {
      setIsJobDescriptionInvalid(true);
      toast.error("校验错误", {
        description: errors.jobDescription,
      });
      return;
    }

    try {
      setIsFormatting(true);

      // 从localStorage获取模型配置
      const MODEL_STORAGE_KEY = 'SnapOffer-default-model';
      const selectedModel = localStorage.getItem(MODEL_STORAGE_KEY);

      // 使用AI格式化岗位信息
      const formattedJob = await formatJobListing(jobDescription, {
        model: selectedModel || 'gpt-4.1-nano'
      });

      setIsFormatting(false);
      setIsCreating(true);

      // 创建岗位到数据库
      const newJob = await createJob(formattedJob);
      
      // 成功回调
      toast.success("成功", {
        description: "岗位添加成功",
      });

      // 通知父组件新岗位已添加
      onJobAdded?.(newJob);
      
      // 关闭对话框并重置状态
      setOpen(false);
      setJobDescription('');
      setIsJobDescriptionInvalid(false);

    } catch (error) {
      console.error('Error creating job:', error);
      
      // 处理特定错误类型
      if (error instanceof Error) {
        if (error.message.toLowerCase().includes('api key') || 
            error.message.toLowerCase().includes('unauthorized') ||
            error.message.toLowerCase().includes('invalid key')) {
          toast.error("API 密钥错误", {
            description: "您的 API 密钥有问题。请检查您的设置并重试。",
          });
        } else {
          toast.error("创建失败", {
            description: error.message,
          });
        }
      } else {
        toast.error("创建失败", {
          description: "创建岗位失败，请重试",
        });
      }
    } finally {
      setIsFormatting(false);
      setIsCreating(false);
    }
  };

  // 处理对话框状态变化
  const handleOpenChange = (newOpen: boolean) => {
    setOpen(newOpen);
    if (newOpen) {
      // 重置状态
      setJobDescription('');
      setIsJobDescriptionInvalid(false);
    }
  };

  const isLoading = isFormatting || isCreating;

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogTrigger asChild>
        <Button>
          <Plus className="w-4 h-4 mr-2" />
          添加申请
        </Button>
      </DialogTrigger>
      
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>添加新的岗位申请</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-6 py-4">
          <div className="space-y-2">
            <label htmlFor="job-description" className="text-sm font-medium text-gray-900">
              岗位描述 <span className="text-red-500">*</span>
            </label>
            <p className="text-sm text-gray-600">
              粘贴完整的岗位描述，AI 将自动解析职位信息
            </p>
            <JobDescriptionInput
              value={jobDescription}
              onChange={(value) => {
                setJobDescription(value);
                setIsJobDescriptionInvalid(false);
              }}
              isInvalid={isJobDescriptionInvalid}
            />
          </div>

          <div className="flex items-center justify-end gap-3 pt-4 border-t">
            <Button
              variant="outline"
              onClick={() => setOpen(false)}
              disabled={isLoading}
            >
              取消
            </Button>
            <Button
              onClick={handleCreateJob}
              disabled={isLoading || !jobDescription.trim()}
            >
              {isLoading && <Loader2 className="w-4 h-4 mr-2 animate-spin" />}
              {isFormatting && "AI解析中..."}
              {isCreating && "创建中..."}
              {!isLoading && "添加岗位"}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
