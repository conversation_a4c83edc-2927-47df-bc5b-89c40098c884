'use client';

import { cn } from "@/lib/utils";
import { Sparkles } from "lucide-react";
import Link from "next/link";
import { motion } from "framer-motion";
import { designTokens } from "@/lib/design-tokens";

interface ProUpgradeButtonProps {
  className?: string;
}

export function ProUpgradeButton({ className }: ProUpgradeButtonProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className={cn("relative group", className)}
    >
      {/* 简化的光晕效果 */}
      <div className="absolute -inset-[3px] rounded-lg opacity-75 blur-md transition-all duration-300 ease-in-out" style={{ backgroundColor: designTokens.colors.blue[500] + '00' }} onMouseEnter={(e) => { e.currentTarget.style.backgroundColor = designTokens.colors.blue[500] + '4D' }} onMouseLeave={(e) => { e.currentTarget.style.backgroundColor = designTokens.colors.blue[500] + '00' }} />

      <Link
        href="/subscription"
        className={cn(
          "relative flex items-center gap-1.5 px-4 py-1.5",
          "bg-blue-500",
          "text-white font-medium rounded-lg",
          "shadow-lg hover:shadow-xl hover:shadow-blue-500/20",
          "transition-all duration-300 ease-in-out",
          "hover:-translate-y-0.5",
          "text-sm"
        )}
      >
        <Sparkles className="h-4 w-4 transition-transform duration-300 ease-in-out group-hover:scale-110" />
        <span className="transition-all duration-300 ease-in-out group-hover:translate-x-0.5">升级到专业版</span>
      </Link>
    </motion.div>
  );
} 