"use client"
import React from 'react';
import Image from "next/image";
import { motion } from "framer-motion";
import { CheckCircle2 } from "lucide-react";
import { SplitContent } from '../ui/split-content';
import { AuthDialog } from "@/components/auth/auth-dialog";
import { designTokens, designTokenUtils } from "@/lib/design-tokens";
import { HowItWorks } from "./how-it-works";

const FeatureHighlights = () => {
  // Enhanced features with metrics, testimonials, and benefit-focused language


  // Trusted by logos
  const companies = [
    { name: "Google", logo: "/logos/google.png" },
    { name: "Microsoft", logo: "/logos/microsoft.webp" },
    { name: "Amazon", logo: "/logos/amazon.png" },
    { name: "<PERSON><PERSON>", logo: "/logos/meta.png" },
    { name: "Netflix", logo: "/logos/netflix.png" },
  ];

  // Statistics counters
  const stats = [
    { value: "500+", label: "已帮助求职" },
    { value: "90%", label: "Offer成功率" },
    { value: "4.9/5", label: "用户评分" },
    { value: "3天", label: "平均获得Offer" },
  ];

  // Animation variants for scroll reveal
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.8,
        ease: "easeOut"
      }
    }
  };

  return (
    <section className="py-12 md:py-16 px-4 sm:px-6 relative overflow-hidden">
      {/* 简化的装饰元素 - 使用设计令牌 */}
      <div className="absolute -top-40 -right-40 w-96 h-96 rounded-full blur-3xl" style={{ backgroundColor: designTokenUtils.withOpacity(designTokens.colors.gray[100], 0.2) }}></div>
      <div className="absolute -bottom-40 -left-40 w-96 h-96 rounded-full blur-3xl" style={{ backgroundColor: designTokenUtils.withOpacity(designTokens.colors.gray[100], 0.2) }}></div>
      <div className="absolute top-1/3 left-1/4 w-64 h-64 rounded-full blur-3xl" style={{ backgroundColor: designTokenUtils.withOpacity(designTokens.colors.gray[100], 0.15) }}></div>
 
      {/* Redesigned heading section with enhanced visual appeal */}
      <div className="relative z-10 max-w-5xl mx-auto">
        {/* Decorative elements specific to the heading - 使用设计令牌 */}
        <div className="absolute -top-28 left-1/2 -translate-x-1/2 w-[800px] h-[800px] rounded-full blur-3xl -z-10" style={{ backgroundColor: designTokenUtils.withOpacity(designTokens.colors.gray[100], 0.15) }}></div>
        <div className="absolute -top-20 -right-20 w-80 h-80 rounded-full blur-3xl -z-10" style={{ backgroundColor: designTokenUtils.withOpacity(designTokens.colors.gray[100], 0.2) }}></div>
        <div className="absolute -bottom-10 -left-20 w-72 h-72 rounded-full blur-3xl -z-10" style={{ backgroundColor: designTokenUtils.withOpacity(designTokens.colors.gray[100], 0.2) }}></div>
        
        {/* Leading badges - 使用设计令牌 */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.1 }}
          className="flex justify-center gap-3 mb-4"
        >
          <span className="px-3 py-1 rounded-full text-sm" style={{ 
            backgroundColor: designTokenUtils.withOpacity(designTokens.colors.gray[100], 0.5),
            border: `1px solid ${designTokenUtils.withOpacity(designTokens.colors.gray[200], 0.4)}`,
            color: designTokens.colors.gray[700]
          }}>
            求职助手
          </span>
          <span className="px-3 py-1 rounded-full text-sm" style={{ 
            backgroundColor: designTokenUtils.withOpacity(designTokens.colors.gray[100], 0.5),
            border: `1px solid ${designTokenUtils.withOpacity(designTokens.colors.gray[200], 0.4)}`,
            color: designTokens.colors.gray[700]
          }}>
            简历定制
          </span>
          <span className="px-3 py-1 rounded-full text-sm" style={{ 
            backgroundColor: designTokenUtils.withOpacity(designTokens.colors.gray[100], 0.5),
            border: `1px solid ${designTokenUtils.withOpacity(designTokens.colors.gray[200], 0.4)}`,
            color: designTokens.colors.gray[700]
          }}>
            求职管理
          </span>
          <span className="px-3 py-1 rounded-full text-sm" style={{ 
            backgroundColor: designTokenUtils.withOpacity(designTokens.colors.gray[100], 0.5),
            border: `1px solid ${designTokenUtils.withOpacity(designTokens.colors.gray[200], 0.4)}`,
            color: designTokens.colors.gray[700]
          }}>
            岗位评估
          </span>
          <span className="px-3 py-1 rounded-full text-sm" style={{ 
            backgroundColor: designTokenUtils.withOpacity(designTokens.colors.gray[100], 0.5),
            border: `1px solid ${designTokenUtils.withOpacity(designTokens.colors.gray[200], 0.4)}`,
            color: designTokens.colors.gray[700]
          }}>
            求职信生成
          </span>
          <span className="px-3 py-1 rounded-full text-sm" style={{ 
            backgroundColor: designTokenUtils.withOpacity(designTokens.colors.gray[100], 0.5),
            border: `1px solid ${designTokenUtils.withOpacity(designTokens.colors.gray[200], 0.4)}`,
            color: designTokens.colors.gray[700]
          }}>
            应聘攻略
          </span>
        </motion.div>
        
        {/* Heading with enhanced typography - 使用设计令牌 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.7, delay: 0.2 }}
          className="text-center mb-4"
        >
          <h2 className="text-4xl md:text-6xl font-bold leading-tight tracking-tight">
            <span className="inline-block" style={{ color: designTokens.colors.gray[900] }}>
              AI求职智能体
            </span>
            <br />
            <motion.span
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.5 }}
              className="inline-block mt-1"
              style={{ color: designTokens.colors.gray[900] }}
            >
              智能Offer收割机
            </motion.span>
          </h2>
          
          <motion.p 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.7 }}
            className="text-lg md:text-xl max-w-2xl mx-auto mt-3"
            style={{ color: designTokens.colors.gray[600] }}
          >
            集成智能求职助手、简历生成、求职管理、岗位评估、求职信生成和应聘攻略，帮助求职者快速理解和评估岗位，快速获得 <span className="font-semibold" style={{ color: designTokens.colors.blue[600] }}>Offer</span>
          </motion.p>
        </motion.div>

        {/* Enhanced statistics with animated reveal - 使用设计令牌 */}
        <motion.div 
          className="flex flex-wrap justify-center gap-8 md:gap-12 mx-auto mt-8"
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
        >
          {stats.map((stat, index) => (
              <motion.div 
                key={index}
                variants={itemVariants}
                className="text-center relative"
              >
                <motion.p 
                  initial={{ scale: 0.9, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  transition={{ duration: 0.5, delay: 0.1 * index }}
                  className="text-3xl md:text-4xl font-bold"
                  style={{ color: designTokens.colors.gray[900] }}
                >
                  {stat.value}
                </motion.p>
                <p className={`text-sm md:text-base mt-1`} style={{ color: designTokens.colors.gray[700] }}>
                  {stat.label}
                </p>
              </motion.div>
            ))}
        </motion.div>
        
        {/* Colorful separators - 使用设计令牌 */}
        <div className="flex justify-center my-12">
          <div className="w-16 h-[3px] rounded-full mx-1" style={{ backgroundColor: designTokens.colors.gray[300] }}></div>
          <div className="w-16 h-[3px] rounded-full mx-1" style={{ backgroundColor: designTokens.colors.gray[300] }}></div>
          <div className="w-16 h-[3px] rounded-full mx-1" style={{ backgroundColor: designTokens.colors.gray[300] }}></div>
        </div>
      </div>
      
      {/* Enhanced Features Section with improved card styling - 使用设计令牌 */}
      <div className="flex flex-col gap-12 py-16 relative" id="features">
            <div className="absolute inset-0" style={{ 
              background: `linear-gradient(to right, transparent, ${designTokenUtils.withOpacity(designTokens.colors.gray[50], 0.3)}, transparent)` 
            }}></div>
            
            <SplitContent
              imageSrc="/Dashboard Image.png"
              heading="智能简历生成器"
              description="使用我们强大的智能简历生成器，根据您的职业背景和目标岗位自动定制优化简历。AI智能分析岗位要求，一键生成个性化简历内容，基于多年专业经验，提供简洁有效的样式排版，重点突出您的核心优势。"
              imageOnLeft={true}
              badgeText="简历质量提升85%"
              badgeGradient="from-purple-600/10 to-pink-600/10"
              bulletPoints={[
                "职业背景智能分析",
                "岗位要求自动匹配",
                "一键生成个性化简历"
              ]}
            />

            <SplitContent
              imageSrc="/SS Chat.png"
              heading="AI智能求职助手"
              description="从我们的高级 AI 助手获得实时反馈和建议。快速分析目标岗位要求，为您定制最合适的简历和应聘策略，确保在众多求职者中脱颖而出。"
              imageOnLeft={false}
              imageOverflowRight={true}
              badgeText="岗位匹配度提升90%"
              badgeGradient="from-gray-600/10 to-gray-600/10"
              bulletPoints={[
                "智能分析岗位要求",
                "个性化简历建议",
                "实时应聘策略指导"
              ]}
            />

            <SplitContent
              imageSrc="/Dashboard Image.png"
              heading="智能求职管理中心"
              description="使用我们直观的仪表板在一个地方管理您的整个求职过程。创建基础简历，为特定岗位生成定制版本，跟踪申请进度和面试安排。"
              imageOnLeft={true}
              badgeText="一站式求职管理"
              badgeGradient="from-gray-600/10 to-gray-600/10"
              bulletPoints={[
                "岗位申请管理",
                "简历版本控制",
                "面试进度跟踪"
              ]}
            />

            <SplitContent
              imageSrc="/SS Score.png"
              heading="智能岗位评估"
              description="通过我们的智能评估系统获得岗位匹配度的详细洞察。分析岗位要求，评估您的竞争力，提供具体的改进建议来提高获得Offer的概率。"
              imageOnLeft={false}
              imageOverflowRight={true}
              badgeText="Offer率提升3倍"
              badgeGradient="from-gray-600/10 to-gray-600/10"
              bulletPoints={[
                "岗位竞争力分析",
                "匹配度评分",
                "个性化改进建议"
              ]}
            />

            <SplitContent
              imageSrc="/SS Cover Letter.png"
              heading="AI求职信生成器"
              description="使用我们的 AI 驱动生成器，快速创建专业的求职信。根据目标岗位要求和您的个人经历，生成个性化的求职信内容，提高申请成功率。"
              imageOnLeft={true}
              badgeText="申请成功率提升60%"
              badgeGradient="from-blue-600/10 to-cyan-600/10"
              bulletPoints={[
                "个性化求职信生成",
                "岗位匹配度优化",
                "专业格式模板"
              ]}
            />

            <SplitContent
              imageSrc="/SS Cover Letter.png"
              heading="AI应聘攻略生成器"
              description="基于深度研究目标公司和业务，生成全方位的应聘攻略。AI会深入研究公司背景、业务模式、团队文化、技术栈等信息，为您制定个性化的面试策略和问题预测。"
              imageOnLeft={false}
              imageOverflowRight={true}
              badgeText="面试通过率提升80%"
              badgeGradient="from-emerald-600/10 to-green-600/10"
              bulletPoints={[
                "公司深度研究",
                "业务模式分析",
                "个性化面试策略"
              ]}
            />
      </div>
      
      {/* How SnapOffer Works Section */}
      <div className="mt-20 mb-8">
        <HowItWorks />
      </div>
      
      {/* Social proof section - Trusted by companies */}
      <motion.div 
        className="mt-16 text-center"
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true, margin: "-100px" }}
        transition={{ duration: 0.8 }}
      >
        <h3 className="text-xl text-muted-foreground mb-8">获得来自以下公司专业人士的信赖</h3>
        <div className="flex flex-wrap justify-center items-center gap-8 md:gap-12 max-w-4xl mx-auto opacity-80">
          {companies.map((company, index) => (
            <div key={index} className="w-24 h-12 relative transition-all duration-300">
              <Image 
                src={company.logo} 
                alt={company.name} 
                fill
                className="object-contain" 
                sizes="100px"
              />
            </div>
          ))}
        </div>
      </motion.div>
      
      {/* Enhanced CTA section - 使用设计令牌 */}
      <motion.div 
        className="mt-20 text-center"
        initial={{ opacity: 0, scale: 0.95 }}
        whileInView={{ opacity: 1, scale: 1 }}
        viewport={{ once: true, margin: "-100px" }}
        transition={{ duration: 0.5 }}
      >
        <div className="max-w-3xl mx-auto px-6 py-12 rounded-2xl backdrop-blur-lg shadow-xl" style={{ 
          background: `linear-gradient(135deg, ${designTokenUtils.withOpacity(designTokens.colors.white, 0.9)}, ${designTokenUtils.withOpacity(designTokens.colors.white, 0.7)})`,
          border: `1px solid ${designTokenUtils.withOpacity(designTokens.colors.white, 0.4)}`
        }}>
          <h2 className="text-3xl md:text-4xl font-bold mb-4" style={{ color: designTokens.colors.gray[900] }}>
            准备好快速获得Offer了吗？
          </h2>
          <p className="text-lg mb-8" style={{ color: designTokens.colors.gray[600] }}>
            加入 500+ 位通过 SnapOffer 快速获得理想Offer的求职者
          </p>
          
          <div className="flex justify-center">
            <AuthDialog>
              <button 
                className="px-10 py-4 rounded-lg text-white text-lg font-medium shadow-lg transition-all duration-300 hover:-translate-y-1 hover:shadow-xl"
                style={{ backgroundColor: designTokens.colors.blue[500] }}
              >
                开始收割Offer
              </button>
            </AuthDialog>
          </div>
          
          <p className="text-sm mt-6 flex items-center justify-center gap-2" style={{ color: designTokens.colors.gray[600] }}>
            <CheckCircle2 className="w-4 h-4" style={{ color: designTokens.colors.green[500] }} />
            无需信用卡 • 立即开始
          </p>
        </div>
      </motion.div>

      {/* Sticky mobile CTA - only visible on mobile/tablet - 使用设计令牌 */}
      <div className="md:hidden fixed bottom-4 left-0 right-0 z-50 px-4">
        <AuthDialog>
          <button 
            className="flex items-center justify-center w-full py-3.5 rounded-lg text-white font-medium shadow-lg"
            style={{ backgroundColor: designTokens.colors.blue[500] }}
          >
            开始收割Offer
          </button>
        </AuthDialog>
      </div>
    </section>
  );
};

export default FeatureHighlights;
