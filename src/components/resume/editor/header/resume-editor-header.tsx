'use client';

import { Resume } from "@/lib/types";
import { Logo } from "@/components/ui/logo";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";
import { useRouter } from "next/navigation";
import { cn } from "@/lib/utils";
import { designTokens } from "@/lib/design-tokens";

interface ResumeEditorHeaderProps {
  resume: Resume;
  hasUnsavedChanges: boolean;
}

export function ResumeEditorHeader({
  resume,
  hasUnsavedChanges,
}: ResumeEditorHeaderProps) {
  const router = useRouter();
  const capitalizeWords = (str: string) => {
    return str.split(' ').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ');
  };

  const handleBackClick = () => {
    if (!hasUnsavedChanges) {
      router.push('/');
    }
  };

  // 统一使用薄荷绿主题，不再区分简历类型
  const colors = {
    gradient: "from-gray-600 via-gray-500 to-gray-400",
    border: "border-gray-200/50",
    background: "from-gray-50/95 via-white/95 to-gray-50/95",
    shadow: "shadow-gray-500/10",
    text: "text-gray-600",
    hover: "hover:text-gray-600",
    textOpacity: "text-gray-600/60",
    gradientOverlay: "#e6f7f230",
  };

  return (
    <div className={cn(
      `h-20 border-b backdrop-blur-xl fixed left-0 right-0 shadow-lg`,
      colors.border,
      "bg-gray-50/95",
      colors.shadow
    )} style={{ zIndex: designTokens.zIndex.fixed }}>
      {/* 简化的背景覆盖层 */}
      <div className={cn(
        "absolute inset-0 opacity-30",
        "bg-white/50",
        "pointer-events-none"
      )} />
      
      {/* Content Container */}
      <div className="max-w-[2000px] mx-auto h-full px-6 flex items-center justify-between relative">
        {/* Left Section - Logo, Title  */}
        <div className="flex items-center gap-6">
          {hasUnsavedChanges ? (
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <div>
                  <Logo className="text-xl cursor-pointer" asLink={false} />
                </div>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>未保存的更改</AlertDialogTitle>
                  <AlertDialogDescription>
                    您有未保存的更改。确定要离开吗？您的更改将丢失。
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>取消</AlertDialogCancel>
                  <AlertDialogAction
                    onClick={() => router.push('/')}
                    className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                  >
                    离开
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          ) : (
            <div onClick={handleBackClick}>
              <Logo className="text-xl cursor-pointer" asLink={false} />
            </div>
          )}
          <div className="h-8 w-px bg-gray-200/50 hidden sm:block" />
          <div className="flex flex-col justify-center gap-1">
            {/* Resume Title Section */}
            <div className="flex flex-col ">
              <h1 className={cn("text-xl font-semibold", colors.text)}>
                {resume.is_base_resume ? capitalizeWords(resume.target_role) : resume.name}
              </h1>
              <div className={cn("flex text-sm", colors.textOpacity)}>
                {resume.is_base_resume ? (
                  <div className="flex items-center">
                    <span className="text-xs font-medium">基础简历</span>
                  </div>
                ) : (
                  <div className="flex items-center gap-2">
                    <span className="text-xs font-medium">定制简历</span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 