'use client';

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { <PERSON><PERSON>, DialogContent, Di<PERSON>T<PERSON>le, DialogTrigger, DialogDescription } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Resume, Profile } from "@/lib/types";
import { toast } from "sonner";
import { <PERSON>ader2, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, Copy } from "lucide-react";
import { createTailoredResume } from "@/utils/actions/resumes/actions";
import { CreateBaseResumeDialog } from "./create-base-resume-dialog";
import { tailorResumeToJob } from "@/utils/actions/jobs/ai";
import { formatJobListing } from "@/utils/actions/jobs/ai";
import { createJob, getJobById } from "@/utils/actions/jobs/actions";
import { z } from "zod";
import { simplifiedJobSchema } from "@/lib/zod-schemas";
import { MiniResumePreview } from "../../shared/mini-resume-preview";
import { LoadingOverlay, type CreationStep } from "../loading-overlay";
import { BaseResumeSelector } from "../base-resume-selector"; 
import { ImportMethodRadioGroup } from "../import-method-radio-group";
import { JobDescriptionInput } from "../job-description-input";
import { ApiErrorDialog } from "@/components/ui/api-error-dialog";
import { cn } from "@/lib/utils";

interface CreateTailoredResumeDialogProps {
  children: React.ReactNode;
  baseResumes?: Resume[];
  profile?: Profile;
  // 新增：预填充的Job信息，跳过JD输入步骤
  prefilledJob?: {
    id: string;
    position_title: string;
    company_name: string;
  };
  // 新增：简历创建成功回调，用于不跳转的场景
  onResumeCreated?: (resume: Resume) => void;
  // 新增：开始创建时的回调，用于显示加载状态
  onStartCreating?: () => void;
  // 新增：外部控制对话框开关
  forceOpen?: boolean;
  onForceOpenChange?: (open: boolean) => void;
}

export function CreateTailoredResumeDialog({ children, baseResumes, profile, prefilledJob, onResumeCreated, onStartCreating, forceOpen, onForceOpenChange }: CreateTailoredResumeDialogProps) {
  const [open, setOpen] = useState(false);
  
  // 处理外部强制打开
  useEffect(() => {
    if (forceOpen) {
      setOpen(true);
      onForceOpenChange?.(false); // 重置强制打开状态
    }
  }, [forceOpen, onForceOpenChange]);
  const [selectedBaseResume, setSelectedBaseResume] = useState<string>(baseResumes?.[0]?.id || '');
  const [jobDescription, setJobDescription] = useState('');
  const [isCreating, setIsCreating] = useState(false);
  const [currentStep, setCurrentStep] = useState<CreationStep>('analyzing');
  // 如果有预填充的Job，直接跳到第一步（选择基础简历），并设置为ai模式进行AI定制
  const [dialogStep, setDialogStep] = useState<1 | 2>(1);
  const [importOption, setImportOption] = useState<'import-profile' | 'ai'>(prefilledJob ? 'ai' : 'ai');
  const [isBaseResumeInvalid, setIsBaseResumeInvalid] = useState(false);
  const [isJobDescriptionInvalid, setIsJobDescriptionInvalid] = useState(false);
  const [showErrorDialog, setShowErrorDialog] = useState(false);
  const [errorMessage, setErrorMessage] = useState({ title: '', description: '' });
  const router = useRouter();
  

  const handleNext = () => {
    if (!selectedBaseResume) {
      setIsBaseResumeInvalid(true);
      toast.error("缺少必填字段", {
        description: "请选择一个基础简历以继续。",
      });
      return;
    }
    setDialogStep(2);
  };

  const handleBack = () => {
    setDialogStep(1);
  };

  const handleCreate = async () => {
    // Validate required fields
    if (!selectedBaseResume) {
      setIsBaseResumeInvalid(true);
      toast.error("错误", {
        description: "请选择一个基础简历",
      });
      return;
    }

    if (!jobDescription.trim() && importOption === 'ai' && !prefilledJob) {
      setIsJobDescriptionInvalid(true);
      toast.error("错误", {
        description: "请输入职位描述",
      });
      return;
    }

    try {
      setIsCreating(true);
      setCurrentStep('analyzing');
      
      // 通知父组件开始创建（用于显示加载状态）
      onStartCreating?.();
      
      // Reset validation states
      setIsBaseResumeInvalid(false);
      setIsJobDescriptionInvalid(false);

      // 获取基础简历
      const baseResume = baseResumes?.find(r => r.id === selectedBaseResume);
      if (!baseResume) throw new Error("未找到基础简历");

      let jobId: string | null = null;
      let jobTitle = '复制简历';
      let companyName = '';
      let formattedJobListing: z.infer<typeof simplifiedJobSchema> | null = null;

      // 获取模型配置
      const MODEL_STORAGE_KEY = 'SnapOffer-default-model';
      const selectedModel = localStorage.getItem(MODEL_STORAGE_KEY);

      if (prefilledJob) {
        // 使用预填充的Job信息，获取完整岗位数据进行AI定制
        try {
          setCurrentStep('analyzing');
          const fullJob = await getJobById(prefilledJob.id);
          if (!fullJob) throw new Error("无法找到岗位信息");

          jobId = fullJob.id;
          jobTitle = fullJob.position_title;
          companyName = fullJob.company_name;

          // 将现有岗位数据转换为formatJobListing的格式
          formattedJobListing = {
            company_name: fullJob.company_name,
            position_title: fullJob.position_title,
            job_url: fullJob.job_url,
            description: fullJob.description,
            location: fullJob.location,
            salary_range: fullJob.salary_range,
            keywords: fullJob.keywords,
            work_location: fullJob.work_location,
            employment_type: fullJob.employment_type
          };
        } catch (error) {
          console.error('Error fetching job details:', error);
          throw new Error("获取岗位详情失败");
        }

        // 对于prefilledJob，默认进行AI定制
        try {
          setCurrentStep('tailoring');
          
          // 使用AI定制简历内容
          const tailoredContent = await tailorResumeToJob(baseResume, formattedJobListing, {
            model: selectedModel || 'gpt-4.1-nano'
          });

          setCurrentStep('finalizing');

          // 创建定制简历
          const resume = await createTailoredResume(
            baseResume,
            jobId,
            jobTitle,
            companyName,
            tailoredContent,
          );

          toast.success("成功", {
            description: "简历创建成功",
          });

          // 如果有回调，使用回调而不是跳转
          if (onResumeCreated) {
            onResumeCreated(resume);
            setOpen(false);
          } else {
            router.push(`/resumes/${resume.id}`);
            setOpen(false);
          }
          return;
        } catch (error: Error | unknown) {
          if (error instanceof Error && (
              error.message.toLowerCase().includes('api key') || 
              error.message.toLowerCase().includes('unauthorized') ||
              error.message.toLowerCase().includes('invalid key'))
          ) {
            setErrorMessage({
              title: "API 密钥错误",
              description: "您的 API 密钥有问题。请检查您的设置并重试。"
            });
          } else {
            setErrorMessage({
              title: "错误",
              description: "无法定制简历。请重试。"
            });
          }
          setShowErrorDialog(true);
          setIsCreating(false);
          return;
        }
      } else if (importOption === 'import-profile') {
        // 直接复制模式（无AI定制）
        if (jobDescription.trim()) {
          // Use already defined selectedModel

          try {
            setCurrentStep('analyzing');
            formattedJobListing = await formatJobListing(jobDescription, {
              model: selectedModel || 'gpt-4.1-nano'
            });

            setCurrentStep('formatting');
            const jobEntry = await createJob(formattedJobListing);
            if (!jobEntry?.id) throw new Error("无法创建工作项目");
            
            jobId = jobEntry.id;
            jobTitle = formattedJobListing.position_title || '复制简历';
            companyName = formattedJobListing.company_name || '';
          } catch (error: Error | unknown) {
            if (error instanceof Error && (
                error.message.toLowerCase().includes('api key') || 
                error.message.toLowerCase().includes('unauthorized') ||
                error.message.toLowerCase().includes('invalid key'))
            ) {
              setErrorMessage({
                title: "API 密钥错误",
                description: "您的 API 密钥有问题。请检查您的设置并重试。"
              });
            } else {
              setErrorMessage({
                title: "错误",
                description: "无法处理职位描述。请重试。"
              });
            }
            setShowErrorDialog(true);
            setIsCreating(false);
            return;
          }
        }

        const resume = await createTailoredResume(
          baseResume,
          jobId,
          jobTitle,
          companyName,
          {
            work_experience: baseResume.work_experience,
            education: baseResume.education.map(edu => ({
              ...edu,
              gpa: edu.gpa?.toString()
            })),
            skills: baseResume.skills,
            projects: baseResume.projects,
            target_role: baseResume.target_role
          }
        );

        toast.success("成功", {
          description: "简历创建成功",
        });

        // 如果有回调，使用回调而不是跳转
        if (onResumeCreated) {
          onResumeCreated(resume);
          setOpen(false);
        } else {
          router.push(`/resumes/${resume.id}`);
          setOpen(false);
        }
        return;
      }

      // 1. Format the job listing using already defined variables
      try {
        formattedJobListing = await formatJobListing(jobDescription, {
          model: selectedModel || 'gpt-4.1-nano'
        });
      } catch (error: Error | unknown) {
        if (error instanceof Error && (
            error.message.toLowerCase().includes('api key') || 
            error.message.toLowerCase().includes('unauthorized') ||
            error.message.toLowerCase().includes('invalid key'))
        ) {
          setErrorMessage({
            title: "API 密钥错误",
            description: "您的 API 密钥有问题。请检查您的设置并重试。"
          });
        } else {
          setErrorMessage({
            title: "错误",
            description: "无法分析职位描述。请重试。"
          });
        }
        setShowErrorDialog(true);
        setIsCreating(false);
        return;
      }

      setCurrentStep('formatting');

      // 2. Create job in database and get ID
      const jobEntry = await createJob(formattedJobListing);
      if (!jobEntry?.id) throw new Error("无法创建工作项目");


      // 3. Use the base resume object (already defined above)
      if (!baseResume) throw new Error("未找到基础简历");

      setCurrentStep('tailoring');

      // 4. Tailor the resume using the formatted job listing
      let tailoredContent;

      try {
        tailoredContent = await tailorResumeToJob(baseResume, formattedJobListing, {
          model: selectedModel || 'gpt-4.1-nano'
        });
      } catch (error: Error | unknown) {
        if (error instanceof Error && (
            error.message.toLowerCase().includes('api key') || 
            error.message.toLowerCase().includes('unauthorized') ||
            error.message.toLowerCase().includes('invalid key'))
        ) {
          setErrorMessage({
            title: "API 密钥错误",
            description: "您的 API 密钥有问题。请检查您的设置并重试。"
          });
        } else {
          setErrorMessage({
            title: "错误",
            description: "无法定制简历。请重试。"
          });
        }
        setShowErrorDialog(true);
        setIsCreating(false);
        return;
      }


      setCurrentStep('finalizing');

      
      // 5. Create the tailored resume with job reference
      const resume = await createTailoredResume(
        baseResume,
        jobEntry.id,
        formattedJobListing.position_title || '',
        formattedJobListing.company_name || '',
        tailoredContent,
      );

      toast.success("成功", {
        description: "简历创建成功",
      });

      // 如果有回调，使用回调而不是跳转
      if (onResumeCreated) {
        onResumeCreated(resume);
        setOpen(false);
      } else {
        router.push(`/resumes/${resume.id}`);
        setOpen(false);
      }
    } catch (error: unknown) {
      console.error('Failed to create resume:', error);
      toast.error("错误", {
        description: error instanceof Error ? error.message : "无法创建简历。请重试。",
      });
    } finally {
      setIsCreating(false);
    }
  };

  // Reset form when dialog opens
  const handleOpenChange = (newOpen: boolean) => {
    setOpen(newOpen);
    if (newOpen) {
      setJobDescription('');
      setDialogStep(1);
      // 如果有预填充Job，使用ai模式进行AI定制，否则使用ai模式
      setImportOption(prefilledJob ? 'ai' : 'ai');
      setSelectedBaseResume(baseResumes?.[0]?.id || '');
    }
  };

  if (!baseResumes || baseResumes.length === 0) {
    return (
      <Dialog open={open} onOpenChange={handleOpenChange}>
        <DialogTrigger asChild>
          {children}
        </DialogTrigger>
        <DialogContent className="sm:max-w-[500px] bg-white border border-gray-200 shadow-lg rounded-lg">
          <div className="flex flex-col items-center justify-center p-8 space-y-4">
            <div className="p-3 rounded-lg bg-gray-50 border border-gray-100">
              <Sparkles className="w-6 h-6 text-gray-600" />
            </div>
            <div className="text-center space-y-2 max-w-sm">
              <h3 className="font-semibold text-lg text-gray-900">未找到基础简历</h3>
              <p className="text-sm text-gray-600">
                您需要先创建一个基础简历，然后才能创建定制版本。
              </p>
            </div>
            {profile ? (
              <CreateBaseResumeDialog profile={profile}>
                <Button className="mt-2 bg-gray-600 hover:bg-gray-700 text-white">
                  <Plus className="w-4 h-4 mr-2" />
                  创建基础简历
                </Button>
              </CreateBaseResumeDialog>
            ) : (
              <Button disabled className="mt-2">
                没有可用的个人资料来创建基础简历
              </Button>
            )}
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <>
      <Dialog open={open} onOpenChange={handleOpenChange}>
        <DialogTrigger asChild>
          {children}
        </DialogTrigger>
        <DialogContent className="sm:max-w-[800px] p-0 max-h-[90vh] overflow-y-auto bg-white border border-gray-200 shadow-lg rounded-lg">
          <style jsx global>{`
            @keyframes shake {
              0%, 100% { transform: translateX(0); }
              10%, 30%, 50%, 70%, 90% { transform: translateX(-2px); }
              20%, 40%, 60%, 80% { transform: translateX(2px); }
            }
            .shake {
              animation: shake 0.8s cubic-bezier(.36,.07,.19,.97) both;
            }
          `}</style>
          
          {/* Header */}
          <div className="px-6 py-4 border-b border-gray-100">
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-lg bg-sky-50 border border-sky-100">
                <Sparkles className="w-5 h-5 text-sky-600" />
              </div>
              <div className="flex-1">
                <DialogTitle className="text-lg font-semibold text-gray-900">
                  创建定制简历
                </DialogTitle>
                <DialogDescription className="text-sm text-gray-600">
                  {prefilledJob 
                    ? `为 ${prefilledJob.company_name} 的 ${prefilledJob.position_title} 岗位创建定制简历`
                    : dialogStep === 1 
                      ? "选择一个基础简历开始"
                      : "配置职位详情和定制方法"
                  }
                </DialogDescription>
              </div>
              {/* Step indicator - 仅在没有预填充Job时显示 */}
              {!prefilledJob && (
                <div className="flex items-center gap-2">
                  <div className={cn(
                    "w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium",
                    dialogStep >= 1 ? "bg-gray-600 text-white" : "bg-gray-200 text-gray-600"
                  )}>
                    1
                  </div>
                  <div className={cn(
                    "w-4 h-0.5",
                    dialogStep >= 2 ? "bg-gray-600" : "bg-gray-200"
                  )} />
                  <div className={cn(
                    "w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium",
                    dialogStep >= 2 ? "bg-gray-600 text-white" : "bg-gray-200 text-gray-600"
                  )}>
                    2
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Content */}
          <div className="px-6 py-2 min-h-[400px] relative">
            {isCreating && <LoadingOverlay currentStep={currentStep} />}
            
            {(dialogStep === 1 || prefilledJob) && (
              <div className="space-y-6">
                {/* Header Section */}
                <div className="text-center space-y-2">
                  <div className="inline-flex items-center justify-center w-10 h-10 rounded-full bg-gray-100 from-sky-500 to-blue-600 mb-1">
                    <Sparkles className="w-5 h-5 text-white" />
                  </div>
                  <h3 className="text-xl font-bold text-gray-900">选择基础简历</h3>
                  <p className="text-gray-600 max-w-sm mx-auto text-sm">
                    选择一份基本简历来适配这个工作机会。
                  </p>
                </div>
                
                {/* Resume Selector */}
                <div className="space-y-4">
                  <BaseResumeSelector
                    baseResumes={baseResumes}
                    selectedResumeId={selectedBaseResume}
                    onResumeSelect={setSelectedBaseResume}
                    isInvalid={isBaseResumeInvalid}
                  />
                </div>

              </div>
            )}

            {dialogStep === 2 && !prefilledJob && (
              <div className="space-y-6">

                {/* Selected Resume Summary */}
                <div className="bg-gray-100 from-gray-50 to-blue-50 border border-gray-200 rounded-lg p-3">
                  <div className="flex items-center gap-3">
                    <div className="flex-shrink-0">
                      <MiniResumePreview
                        name={baseResumes.find(r => r.id === selectedBaseResume)?.name || ''}
                        type="base"
                        className="w-10 h-10"
                      />
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2">
                        <span className="text-sm font-medium text-gray-900">基础简历：</span>
                        <span className="text-sm text-gray-700 font-semibold truncate">
                          {baseResumes.find(r => r.id === selectedBaseResume)?.name}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Job Description Section */}
                <div className="space-y-3">
                  <div className="flex items-center gap-3">
                    <div className="w-7 h-7 rounded-full bg-gray-100 flex items-center justify-center">
                      <span className="text-gray-600 font-bold text-sm">1</span>
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900">职位信息 <span className="text-red-500">*</span></h4>
                      <p className="text-xs text-gray-600">粘贴职位发布的详细信息</p>
                    </div>
                  </div>
                  
                  <div className="ml-10">
                    <JobDescriptionInput
                      value={jobDescription}
                      onChange={setJobDescription}
                      isInvalid={isJobDescriptionInvalid}
                    />
                  </div>
                </div>

                {/* Tailoring Method Section */}
                <div className="space-y-3">
                  <div className="flex items-center gap-3">
                    <div className="w-7 h-7 rounded-full bg-gray-100 flex items-center justify-center">
                      <span className="text-gray-600 font-bold text-sm">2</span>
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900">定制方式</h4>
                      <p className="text-xs text-gray-600">请选择您的定制方式</p>
                    </div>
                  </div>
                  
                  <div className="ml-10">
                    <ImportMethodRadioGroup
                      value={importOption}
                      onChange={setImportOption}
                    />
                  </div>
                </div>

                {/* Method Description */}
                {importOption === 'ai' && (
                  <div className="ml-10 bg-gray-100 from-gray-50 to-gray-50 border border-gray-200 rounded-lg p-3">
                    <div className="flex items-start gap-2">
                      <div className="w-5 h-5 rounded-full bg-gray-100 flex items-center justify-center flex-shrink-0 mt-0.5">
                        <Brain className="w-3 h-3 text-gray-600" />
                      </div>
                      <div className="space-y-1">
                        <h5 className="font-medium text-gray-900 text-sm">AI 智能定制流程</h5>
                        <ul className="text-xs text-gray-800 space-y-0.5">
                          <li className="flex items-center gap-2">
                            <div className="w-1 h-1 rounded-full bg-gray-400"></div>
                            分析职位要求和关键词
                          </li>
                          <li className="flex items-center gap-2">
                            <div className="w-1 h-1 rounded-full bg-gray-400"></div>
                            优化您的经历描述
                          </li>
                          <li className="flex items-center gap-2">
                            <div className="w-1 h-1 rounded-full bg-blue-400"></div>
                            突出相关技能与成就
                          </li>
                        </ul>
                      </div>
                    </div>
                  </div>
                )}

                {importOption === 'import-profile' && (
                  <div className="ml-10 bg-gray-100 from-amber-50 to-orange-50 border border-amber-200 rounded-lg p-3">
                    <div className="flex items-start gap-2">
                      <div className="w-5 h-5 rounded-full bg-amber-100 flex items-center justify-center flex-shrink-0 mt-0.5">
                        <Copy className="w-3 h-3 text-amber-600" />
                      </div>
                      <div className="space-y-1">
                        <h5 className="font-medium text-amber-900 text-sm">直接复制流程</h5>
                        <ul className="text-xs text-amber-800 space-y-0.5">
                          <li className="flex items-center gap-2">
                            <div className="w-1 h-1 rounded-full bg-amber-400"></div>
                            创建您的基础简历的精确副本
                          </li>
                          <li className="flex items-center gap-2">
                            <div className="w-1 h-1 rounded-full bg-amber-400"></div>
                            将其链接到招聘信息以组织
                          </li>
                          <li className="flex items-center gap-2">
                            <div className="w-1 h-1 rounded-full bg-amber-400"></div>
                            之后您可以手动编辑它
                          </li>
                        </ul>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="px-6 py-4 border-t border-gray-100 bg-gray-50/50">
            <div className="flex justify-between">
              <div>
                {dialogStep === 2 && !prefilledJob && (
                  <Button variant="outline" onClick={handleBack} size="sm">
                    返回
                  </Button>
                )}
              </div>
              <div className="flex gap-2">
                <Button variant="outline" onClick={() => setOpen(false)} size="sm">
                  取消
                </Button>
                {/* 有预填充Job时，第一步直接显示创建按钮 */}
                {prefilledJob ? (
                  <Button 
                    onClick={handleCreate} 
                    disabled={isCreating}
                    size="sm"
                    className="bg-sky-600 hover:bg-sky-700 text-white"
                  >
                    {isCreating ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        创建中...
                      </>
                    ) : (
                      '创建简历'
                    )}
                  </Button>
                ) : (
                  <>
                    {dialogStep === 1 && (
                      <Button onClick={handleNext} size="sm" className="bg-sky-600 hover:bg-sky-700">
                        下一步
                      </Button>
                    )}
                    {dialogStep === 2 && (
                      <Button 
                        onClick={handleCreate} 
                        disabled={isCreating}
                        size="sm"
                        className="bg-sky-600 hover:bg-sky-700 text-white"
                      >
                        {isCreating ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            创建中...
                          </>
                        ) : (
                          '创建简历'
                        )}
                      </Button>
                    )}
                  </>
                )}
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Error Dialog */}
      <ApiErrorDialog
        open={showErrorDialog}
        onOpenChange={setShowErrorDialog}
        errorMessage={errorMessage}
        onUpgrade={() => {
          setShowErrorDialog(false);
          window.location.href = '/subscription';
        }}
        onSettings={() => {
          setShowErrorDialog(false);
          window.location.href = '/settings';
        }}
      />
    </>
  );
} 