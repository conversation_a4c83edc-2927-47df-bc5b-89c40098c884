'use client';

import { useState, useEffect } from 'react';
import { createClient } from '@/utils/supabase/client';
import Link from 'next/link';
import { AuthDialog } from '@/components/auth/auth-dialog';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';

interface SmartNavButtonProps {
  className?: string;
}

export function SmartNavButton({ 
  className = ''
}: SmartNavButtonProps) {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    checkAuthStatus();
    
    // 监听认证状态变化
    const { data: { subscription } } = createClient().auth.onAuthStateChange(
      (event, session) => {
        setIsAuthenticated(!!session);
        setIsLoading(false);
      }
    );

    return () => subscription.unsubscribe();
  }, []);

  const checkAuthStatus = async () => {
    try {
      const { data: { session } } = await createClient().auth.getSession();
      setIsAuthenticated(!!session);
    } catch {
      setError('认证状态检查失败');
      // 显示错误提示，但不阻断用户登录流程
      toast.error('认证状态检查失败，请重试登录');
    } finally {
      setIsLoading(false);
    }
  };



  if (isLoading) {
    return (
      <span 
        className={cn(
          "text-sm font-medium text-gray-700",
          className
        )}
      >
        <span className="animate-pulse">加载中</span>
      </span>
    );
  }

  if (error) {
    // 错误态降级为可操作的登录按钮，不阻断用户流程
    return (
      <AuthDialog>
        <button
          className={cn(
            "text-sm font-medium text-gray-700 hover:text-blue-500 transition-colors duration-200",
            "bg-transparent border-none cursor-pointer",
            className
          )}
          title="认证状态检查失败，点击重试登录"
        >
          登录
        </button>
      </AuthDialog>
    );
  }

  if (isAuthenticated) {
    return (
      <Link 
        href="/home"
        className={cn(
          "text-sm font-medium text-gray-700 hover:text-blue-500 transition-colors duration-200",
          className
        )}
      >
        工作台
      </Link>
    );
  }

  return (
    <AuthDialog>
      <button
        className={cn(
          "text-sm font-medium text-gray-700 hover:text-blue-500 transition-colors duration-200",
          "bg-transparent border-none cursor-pointer",
          className
        )}
      >
        登录
      </button>
    </AuthDialog>
  );
}
