'use client';

import { Zap } from "lucide-react";
import Link from "next/link";
import { cn } from "@/lib/utils";
import { designTokens } from "@/lib/design-tokens";

interface LogoProps {
  className?: string;
  asLink?: boolean;
  href?: string;
}

export function Logo({ className, asLink = true, href = "/home" }: LogoProps) {
  const logoContent = (
    <div className={cn("flex items-center gap-1.5", className)}>
      <Zap className="h-6 w-6" style={{ color: designTokens.colors.blue[500] }} />
      <span className="text-2xl font-bold text-black">SnapOffer</span>
    </div>
  );

  if (asLink) {
    return (
      <Link href={href} className="hover:opacity-80 transition-opacity">
        {logoContent}
      </Link>
    );
  }

  return logoContent;
}