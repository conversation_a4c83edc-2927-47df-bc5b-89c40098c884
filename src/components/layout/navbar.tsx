'use client';

// 🚨 DEPRECATED: 此组件已废弃，不再使用
// 废弃原因: 被 AppHeader 组件替代
// 废弃时间: 2025-01-09
// 维护说明: 保留此文件仅作历史参考，请勿在新代码中使用

import Link from "next/link";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";

import { LogoutButton } from "@/components/auth/logout-button";

export function Navbar() {
  const pathname = usePathname();

  const isActive = (path: string) => {
    return pathname === path;
  };

  return (
    <nav className="h-16 border-b bg-white/50 backdrop-blur-lg sticky top-0 w-full z-50">
      <div className="max-w-[2000px] mx-auto h-full px-6 flex items-center justify-between">
        <div className="flex items-center gap-8">
          {/* Logo/Brand */}
          <Link href="/home" className="text-xl font-semibold text-gray-900 hover:text-gray-900 transition-colors">
            SnapOffer
          </Link>

          {/* Navigation Links */}
          <div className="flex items-center gap-6">
            <Link 
              href="/home" 
              className={cn(
                "text-sm transition-colors hover:text-gray-900",
                isActive('/home') ? "text-gray-900 font-medium" : "text-gray-700"
              )}
            >
              仪表板
            </Link>
            <Link 
              href="/jobs" 
              className={cn(
                "text-sm transition-colors hover:text-gray-900",
                isActive('/jobs') ? "text-gray-900 font-medium" : "text-gray-700"
              )}
            >
              职位
            </Link>
          </div>
        </div>

        {/* Right Side Actions */}
        <div className="flex items-center gap-4">
          <LogoutButton />
        </div>
      </div>
    </nav>
  );
} 