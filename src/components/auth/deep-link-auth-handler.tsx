'use client'

import { useEffect, useState } from 'react'
import { AuthDialog } from '@/components/auth/auth-dialog'

type AuthMode = 'login' | 'signup' | null

export function DeepLinkAuthHandler() {
  const [open, setOpen] = useState(false)
  const [tab, setTab] = useState<AuthMode>(null)

  useEffect(() => {
    try {
      const params = new URLSearchParams(typeof window !== 'undefined' ? window.location.search : '')
      const mode = params.get('auth')
      if (mode === 'login' || mode === 'signup') {
        setTab(mode)
        setOpen(true)
        // 清理 URL 参数，避免刷新重复打开
        const url = new URL(window.location.href)
        url.searchParams.delete('auth')
        window.history.replaceState({}, '', url.toString())
      }
    } catch {
      // 忽略 URL 解析异常，保持页面可用
    }
  }, [])

  if (!open || !tab) return null

  return (
    <AuthDialog defaultOpen defaultTab={tab} onOpenChange={setOpen} />
  )
}

export default DeepLinkAuthHandler


