import { createServerClient } from '@supabase/ssr'
import { NextResponse, type NextRequest } from 'next/server'

export async function updateSession(request: NextRequest) {
  const isProd = process.env.NODE_ENV === 'production'
  const isDebug = process.env.AUTH_DEBUG === '1' && !isProd
  // 调试日志（非生产且显式开启 AUTH_DEBUG 时）
  if (isDebug) {
    console.debug('🔍 中间件运行路径:', request.nextUrl.pathname)
  }
  
  let supabaseResponse = NextResponse.next({
    request,
  })

  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return request.cookies.getAll()
        },
        setAll(cookiesToSet) {
          cookiesToSet.forEach(({ name, value }) => request.cookies.set(name, value))
          supabaseResponse = NextResponse.next({
            request,
          })
          cookiesToSet.forEach(({ name, value, options }) =>
            supabaseResponse.cookies.set(name, value, options)
          )
        },
      },
    }
  )

  // Do not run code between createServerClient and
  // supabase.auth.getUser(). A simple mistake could make it very hard to debug
  // issues with users being randomly logged out.

  // IMPORTANT: DO NOT REMOVE auth.getUser()

  const {
    data: { user },
  } = await supabase.auth.getUser()
  
  if (isDebug) {
    console.debug('👤 用户认证状态:', !!user)
  }

  // Create a new headers object with the existing headers
  // Given an incoming request...
  const requestHeaders = new Headers(request.headers)


  // Create new response with enriched headers
  supabaseResponse = NextResponse.next({
    request: {
      ...request,
      headers: requestHeaders,
    },
  })

  supabaseResponse.cookies.set('show-banner', 'false')

  // === Auth helper routes allowlist ===
  // 变更记录: 2025-08-10 by Assistant
  // 目的: 放行认证辅助路由，避免未登录时被中间件重定向
  // 影响范围: 仅 /auth/callback, /auth/confirm, /auth/reset-password, /auth/update-password
  // 回滚方式: 删除本段早返回逻辑即可
  const authHelperRoutes = [
    '/auth/callback',
    '/auth/confirm',
    '/auth/reset-password',
    '/auth/update-password',
  ] as const
  const pathname = request.nextUrl.pathname
  const isAuthHelperRoute = authHelperRoutes.some((prefix) => pathname.startsWith(prefix))
  if (isAuthHelperRoute) {
    if (isDebug) {
      console.debug('🔓 放行认证辅助路由:', pathname)
    }
    return supabaseResponse
  }

  // Check if user is authenticated and redirect if needed
  if (!user) {
    if (isDebug) {
      console.debug('🛡️ 未认证，重定向:', pathname, '-> /')
    }
    const url = request.nextUrl.clone()
    url.pathname = '/'
    
    // Add redirect callback parameter for protected routes
    if (request.nextUrl.pathname !== '/') {
      url.searchParams.set('redirect', request.nextUrl.pathname)
    }
    if (isDebug) {
      console.debug('🔄 重定向地址:', url.toString())
    }
    return NextResponse.redirect(url)
  }

  if (isDebug) {
    console.debug('✅ 用户已认证，允许访问')
  }

  // IMPORTANT: You *must* return the supabaseResponse object as it is.
  // If you're creating a new response object with NextResponse.next() make sure to:
  // 1. Pass the request in it, like so:
  //    const myNewResponse = NextResponse.next({ request })
  // 2. Copy over the cookies, like so:
  //    myNewResponse.cookies.setAll(supabaseResponse.cookies.getAll())
  // 3. Change the myNewResponse object to fit your needs, but avoid changing
  //    the cookies!
  // 4. Finally:
  //    return myNewResponse
  // If this is not done, you may be causing the browser and server to go out
  // of sync and terminate the user's session prematurely!

  return supabaseResponse
}