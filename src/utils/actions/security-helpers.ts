/**
 * 安全工具函数 - 用于确保 Server Actions 的安全性
 * 防止数据泄露和越权访问
 */

import { createClient } from "@/utils/supabase/server";

/**
 * 验证用户身份并返回用户ID
 * @throws {Error} 如果用户未认证
 * @returns {string} 用户ID
 */
export async function requireAuth(): Promise<string> {
  const supabase = await createClient();
  const { data: { user }, error } = await supabase.auth.getUser();
  
  if (error || !user) {
    throw new Error('User not authenticated');
  }
  
  return user.id;
}

/**
 * 创建带有用户过滤的 Supabase 查询
 * @param table 表名
 * @param userId 用户ID
 * @returns 带有用户过滤的查询构建器
 */
export async function createUserFilteredQuery(table: string, userId?: string) {
  const supabase = await createClient();
  
  if (!userId) {
    userId = await requireAuth();
  }
  
  return supabase
    .from(table)
    .select('*')
    .eq('user_id', userId);
}

/**
 * 验证用户是否有权限访问指定资源
 * @param table 表名
 * @param resourceId 资源ID
 * @param userId 用户ID (可选，如果不提供则从当前会话获取)
 * @returns {Promise<boolean>} 是否有权限
 */
export async function validateUserAccess(
  table: string, 
  resourceId: string, 
  userId?: string
): Promise<boolean> {
  const supabase = await createClient();
  
  if (!userId) {
    const authUser = await requireAuth();
    userId = authUser;
  }
  
  const { data, error } = await supabase
    .from(table)
    .select('id')
    .eq('id', resourceId)
    .eq('user_id', userId)
    .single();
  
  return !error && data !== null;
}

/**
 * 安全的删除操作包装器
 * 确保用户只能删除自己的资源
 * @param table 表名
 * @param resourceId 资源ID
 * @param updateData 更新数据 (默认为 { is_active: false })
 * @returns {Promise<{ success: boolean; error?: string }>}
 */
export async function safeDelete(
  table: string,
  resourceId: string,
  updateData: Record<string, unknown> = { is_active: false }
): Promise<{ success: boolean; error?: string }> {
  try {
    const userId = await requireAuth();
    const supabase = await createClient();
    
    const { error } = await supabase
      .from(table)
      .update(updateData)
      .eq('id', resourceId)
      .eq('user_id', userId);
    
    if (error) {
      return { success: false, error: error.message };
    }
    
    return { success: true };
  } catch (error) {
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

/**
 * 带有用户过滤的分页查询构建器
 * @param table 表名
 * @param options 查询选项
 * @returns 分页查询结果
 */
export async function createPaginatedUserQuery<T = unknown>(
  table: string,
  options: {
    page?: number;
    pageSize?: number;
    filters?: Record<string, unknown>;
    orderBy?: { column: string; ascending?: boolean };
  } = {}
) {
  const userId = await requireAuth();
  const supabase = await createClient();
  
  const { page = 1, pageSize = 10, filters = {}, orderBy = { column: 'created_at', ascending: false } } = options;
  const offset = (page - 1) * pageSize;
  
  let query = supabase
    .from(table)
    .select('*', { count: 'exact' })
    .eq('user_id', userId);
  
  // 应用过滤器
  Object.entries(filters).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      if (Array.isArray(value)) {
        query = query.contains(key, value);
      } else {
        query = query.eq(key, value);
      }
    }
  });
  
  // 应用排序
  query = query.order(orderBy.column, { ascending: orderBy.ascending });
  
  // 应用分页
  const { data, error, count } = await query.range(offset, offset + pageSize - 1);
  
  if (error) {
    throw new Error(`Failed to fetch ${table}: ${error.message}`);
  }
  
  return {
    data: data as T[],
    totalCount: count ?? 0,
    currentPage: page,
    totalPages: Math.ceil((count ?? 0) / pageSize)
  };
}

/**
 * 带有用户过滤的无限滚动查询构建器
 * @param table 表名
 * @param options 查询选项
 * @returns 无限滚动查询结果
 */
export async function createInfiniteUserQuery<T = unknown>(
  table: string,
  options: {
    pageSize?: number;
    cursor?: { created_at: string; id: string };
    filters?: Record<string, unknown>;
  } = {}
) {
  const userId = await requireAuth();
  const supabase = await createClient();
  
  const { pageSize = 6, cursor, filters = {} } = options;
  
  let query = supabase
    .from(table)
    .select('*')
    .eq('user_id', userId)
    .order('created_at', { ascending: false })
    .order('id', { ascending: false });
  
  // 应用游标分页
  if (cursor) {
    query = query.or(`created_at.lt.${cursor.created_at},and(created_at.eq.${cursor.created_at},id.lt.${cursor.id})`);
  }
  
  // 应用过滤器
  Object.entries(filters).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      if (Array.isArray(value)) {
        query = query.contains(key, value);
      } else {
        query = query.eq(key, value);
      }
    }
  });
  
  const { data, error } = await query.limit(pageSize);
  
  if (error) {
    throw new Error(`Failed to fetch ${table}: ${error.message}`);
  }
  
  const hasMore = data && data.length === pageSize;
  const nextCursor = data && data.length > 0 ? {
    created_at: data[data.length - 1].created_at,
    id: data[data.length - 1].id
  } : null;
  
  return {
    data: data as T[],
    hasMore,
    nextCursor
  };
}