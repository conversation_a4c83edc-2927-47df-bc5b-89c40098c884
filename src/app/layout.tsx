// import { Inter } from "next/font/google";
import "./globals.css";

// Promise.withResolvers polyfill for compatibility
if (typeof Promise !== 'undefined' && !Promise.withResolvers) {
  Promise.withResolvers = function<T>() {
    let resolve: (value: T | PromiseLike<T>) => void;
    let reject: (reason?: unknown) => void;
    const promise = new Promise<T>((res, rej) => {
      resolve = res;
      reject = rej;
    });
    return { promise, resolve: resolve!, reject: reject! };
  };
}

import { Toaster } from "sonner";
// [2025-08-09] 修改记录：按需隐藏登录后的底栏显示（保留未登录首页底栏）。
// 原导入保留为注释以便回滚：
// import { Footer } from "@/components/layout/footer";
import { AppHeader } from "@/components/layout/app-header";
import { createClient } from "@/utils/supabase/server";
import { getSubscriptionStatus } from '@/utils/actions/stripe/actions';
import { Metadata } from "next";
import { Analytics } from "@vercel/analytics/react"

// const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  metadataBase: new URL("https://SnapOffer.com"),
  title: {
    default: "SnapOffer - AI求职智能体",
    template: "%s | SnapOffer"
  },
  description: "智能求职管理工具，快速为目标岗位定制简历和应聘攻略，帮助求职者快速理解和评估岗位，快速获得Offer。",
  applicationName: "SnapOffer",
  keywords: ["AI求职智能体", "简历构建器", "应聘攻略", "求职管理", "岗位分析", "Offer收割机", "智能求职"],
  authors: [{ name: "SnapOffer" }],
  creator: "SnapOffer",
  publisher: "SnapOffer",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  icons: {
    icon: "/favicon.svg",
    shortcut: "/favicon-32x32.svg",
    apple: "/favicon.svg",
  },
  // manifest: "/site.webmanifest",
  openGraph: {
    type: "website",
    siteName: "SnapOffer",
    title: "SnapOffer - AI求职智能体",
    description: "智能求职管理工具，快速为目标岗位定制简历和应聘攻略，帮助求职者快速理解和评估岗位，快速获得Offer。",
    images: [
      {
        url: "/og.webp",
        width: 1200,
        height: 630,
        alt: "SnapOffer - AI求职智能体 - 智能Offer收割机",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "SnapOffer - AI求职智能体",
    description: "智能求职管理工具，快速为目标岗位定制简历和应聘攻略，帮助求职者快速理解和评估岗位，快速获得Offer。",
    images: ["/og.webp"],
    creator: "@SnapOffer",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  // verification: {
  //   google: "google-site-verification-code", // Replace with actual verification code
  // },
};

export default async function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const supabase = await createClient();
  const { data: { user } } = await supabase.auth.getUser();
  
  let showUpgradeButton = false;
  let isProPlan = false;
  if (user) {
    try {
      const profile = await getSubscriptionStatus();
      const isPro = profile?.subscription_plan?.toLowerCase()?.includes('pro') && 
                    profile?.subscription_status !== 'canceled';
      isProPlan = isPro || false;
      // Show upgrade button only if user is not on pro plan or has canceled
      showUpgradeButton = !isPro;
    } catch {
      // If there's an error, we'll show the upgrade button by default
      showUpgradeButton = true;
      isProPlan = false;
    }
  }

  return (
    <html lang="en">
      <head>
        {/* Preload Chinese fonts for better PDF generation performance */}
        <link
          rel="preload"
          href="/fonts/NotoSansCJKsc-Regular.ttf"
          as="font"
          type="font/ttf"
          crossOrigin="anonymous"
        />
        <link
          rel="preload"
          href="/fonts/NotoSansCJKsc-Bold.ttf"
          as="font"
          type="font/ttf"
          crossOrigin="anonymous"
        />
      </head>
      <body>
        <div className="relative min-h-screen h-screen flex flex-col">
          {user && <AppHeader showUpgradeButton={showUpgradeButton} isProPlan={isProPlan} />}
          {/* Padding for header and footer */}
          <main className="py-14 h-full">
            {children}
            <Analytics />
          </main>
          {/* [2025-08-09] 修改记录：隐藏登录后的底栏 Footer（需求：仅移除登录态底边栏显示，首页保留） */}
          {/* 原代码：{user && <Footer /> } */}
        </div>
        <Toaster 
          richColors 
          position="top-right" 
          closeButton 
          toastOptions={{
            style: {
              fontSize: '1rem',
              padding: '16px',
              minWidth: '280px',
              maxWidth: 'min(500px, calc(100vw - 32px))',
              marginRight: '16px'
            }
          }}
        />
      </body>
    </html>
  );
}
