'use client';

import { useState, useRef, useEffect } from "react";
import { useRouter } from "next/navigation";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { JobListingsCard, JobListingsCardRef } from "@/components/jobs/job-listings-card";
// import { AddJobDialog } from "@/components/jobs/add-job-dialog"; // 回滚说明：取消注释并移除下面的 InlineJobComposer 调用以恢复原弹窗模式
import { InlineJobComposer } from "@/components/jobs/inline-job-composer";
import { Job } from "@/lib/types";
import { createClient } from "@/utils/supabase/client";

// 申请阶段类型（中文）
type ApplicationStage = '全部' | '申请' | '笔试' | '面试' | '谈判' | '录用' | '拒绝';

export default function JobsPage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [selectedStage, setSelectedStage] = useState<ApplicationStage>('全部');
  const [autoOpenJobId, setAutoOpenJobId] = useState<string | null>(null);
  const jobListingsRef = useRef<JobListingsCardRef>(null);

  // 客户端认证检查
  useEffect(() => {
    const checkAuth = async () => {
      try {
        const supabase = createClient();
        const { data: { user }, error } = await supabase.auth.getUser();
        
        if (error || !user) {
          console.log('🚫 未认证用户尝试访问 Jobs 页面，重定向到首页');
          router.push('/');
          return;
        }
        
        console.log('✅ 用户已认证，允许访问 Jobs 页面');
        setIsLoading(false);
      } catch (error) {
        console.error('认证检查失败:', error);
        router.push('/');
      }
    };

    checkAuth();
  }, [router]);

  const handleJobAdded = (newJob: Job) => {
    // 设置要自动打开的岗位ID
    setAutoOpenJobId(newJob.id);
    // 使用增量添加而不是全量刷新，提升性能
    jobListingsRef.current?.addJob(newJob);
  };

  // 显示加载状态
  if (isLoading) {
    return (
      <div className="container max-w-6xl mx-auto flex flex-col gap-8 p-8 pb-32">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
            <p className="text-gray-600">正在验证身份...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="container max-w-6xl mx-auto flex flex-col gap-8 p-8 pb-32">
        {/* 页面标题与操作栏 - Vercel风格，响应式布局优化 */}
        <div className="flex flex-col gap-3 md:flex-row md:items-center md:justify-between md:gap-0">
          <div>
            <h1 className="text-2xl font-semibold tracking-tight">求职申请管理</h1>
            <p className="text-muted-foreground">管理您的求职申请和面试进度</p>
          </div>
          
          {/* 阶段筛选 - 响应式：小屏下独占一行，大屏右对齐 */}
          <div className="w-full md:w-auto">
            <Select
              value={selectedStage}
              onValueChange={(value: ApplicationStage) => setSelectedStage(value)}
            >
              <SelectTrigger className="w-full md:w-[160px]">
                <SelectValue placeholder="筛选阶段" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="全部">全部</SelectItem>
                <SelectItem value="申请">申请</SelectItem>
                <SelectItem value="笔试">笔试</SelectItem>
                <SelectItem value="面试">面试</SelectItem>
                <SelectItem value="谈判">谈判</SelectItem>
                <SelectItem value="录用">录用</SelectItem>
                <SelectItem value="拒绝">拒绝</SelectItem>
              </SelectContent>
            </Select>
            {/* 原添加申请按钮已移至底部固定输入条 */}
          </div>
        </div>

        {/* 岗位列表组件 */}
        <JobListingsCard 
          ref={jobListingsRef} 
          selectedStage={selectedStage} 
          autoOpenJobId={autoOpenJobId}
          onJobOpened={() => setAutoOpenJobId(null)}
        />
      </div>

      {/* 底部固定输入条 */}
      <InlineJobComposer onJobAdded={handleJobAdded} />
    </>
  );
}
