# SnapOffer 命名规范

## 概述

本文档定义了 SnapOffer 项目的命名规范，包括文件、目录、路由参数、URL、组件、变量、函数等命名约定。遵循这些规范有助于保持代码的一致性和可维护性。

## 1. 文件命名规范

### 1.1 通用文件命名

- **使用 kebab-case (短横线命名法)**
- **小写字母**
- **有意义的名称**
- **避免缩写（除非广泛认可）**

```bash
# ✅ 正确示例
user-profile.tsx
job-listings-card.tsx
resume-editor-actions.tsx
api-keys-form.tsx

# ❌ 错误示例
UserProfile.tsx
JobListingsCard.tsx
resumeEditorActions.tsx
APIKeysForm.tsx
```

### 1.2 组件文件命名

- **使用 PascalCase 仅用于组件导出名称**
- **文件名使用 kebab-case**
- **组件类型后缀**

```bash
# ✅ 正确示例
user-profile.tsx          # 导出 UserProfile
job-listings-card.tsx     # 导出 JobListingsCard
resume-editor.tsx        # 导出 ResumeEditor

# ❌ 错误示例
UserProfile.tsx
JobListingsCard.tsx
```

### 1.3 页面文件命名

- **页面文件必须命名为 `page.tsx`**
- **布局文件必须命名为 `layout.tsx`**
- **路由文件必须命名为 `route.ts`**
- **模板文件必须命名为 `template.tsx`**
- **加载文件必须命名为 `loading.tsx`**
- **错误文件必须命名为 `error.tsx`**

```bash
# ✅ 正确示例
src/app/(dashboard)/home/<USER>
src/app/(dashboard)/home/<USER>
src/app/auth/login/page.tsx
src/app/auth/login/loading.tsx
src/app/api/chat/route.ts

# ❌ 错误示例
src/app/(dashboard)/home/<USER>
src/app/auth/login/LoginPage.tsx
src/app/api/chat/chatRoute.ts
```

### 1.4 测试文件命名

- **测试文件与被测试文件同名**
- **添加 `.test.ts` 或 `.test.tsx` 后缀**
- **测试目录使用 `__tests__`**

```bash
# ✅ 正确示例
src/components/ui/button.test.tsx
src/utils/actions/jobs/__tests__/actions.test.ts

# ❌ 错误示例
src/components/ui/buttonTest.tsx
src/utils/actions/jobs/actions.spec.ts
```

### 1.5 类型定义文件命名

- **类型定义文件使用 `.d.ts` 扩展名**
- **与模块同名**

```bash
# ✅ 正确示例
src/types/mdx.d.ts
src/types/html2pdf.d.ts

# ❌ 错误示例
src/types/MdxTypes.d.ts
src/types/Html2PdfTypes.d.ts
```

## 2. 目录命名规范

### 2.1 通用目录命名

- **使用 kebab-case (短横线命名法)**
- **小写字母**
- **单数形式（除非明确表示复数）**

```bash
# ✅ 正确示例
src/components/
src/utils/actions/
src/hooks/
src/types/
src/app/(dashboard)/jobs/

# ❌ 错误示例
src/Components/
src/utils/Actions/
src/Hooks/
src/Types/
src/app/(Dashboard)/Jobs/
```

### 2.2 特殊目录命名

- **路由组目录使用括号：`(dashboard)`**
- **动态路由目录使用方括号：`[user-id]`**
- **并行路由目录使用点号：`@modal`**
- **拦截路由目录使用括号：`(..)login`**

```bash
# ✅ 正确示例
src/app/(dashboard)/
src/app/admin/[user-id]/
src/app/(dashboard)/resumes/@modal/
src/app/(..)login/

# ❌ 错误示例
src/app/dashboard/
src/app/admin/{user-id}/
src/app/dashboard/resumes/modal/
```

## 3. 路由参数和URL规范

### 3.1 路由参数命名

- **使用 kebab-case**
- **小写字母**
- **描述性名称**
- **添加类型后缀（如 `-id`, `-slug`）**

```bash
# ✅ 正确示例
src/app/admin/[user-id]/page.tsx
src/app/blog/[slug]/page.tsx
src/app/(dashboard)/resumes/[id]/page.tsx

# ❌ 错误示例
src/app/admin/[userId]/page.tsx
src/app/blog/[post_slug]/page.tsx
src/app/dashboard/resumes/[resumeId]/page.tsx
```

### 3.2 URL路径规范

- **使用 kebab-case**
- **小写字母**
- **避免下划线**
- **使用复数形式表示集合**

```bash
# ✅ 正确示例
/dashboard/jobs
/dashboard/resumes
/blog/posts
/admin/users

# ❌ 错误示例
/dashboard/Jobs
/dashboard/resume
/blog/posts
/admin/user_list
```

### 3.3 查询参数命名

- **使用 camelCase**
- **描述性名称**
- **避免缩写**

```bash
# ✅ 正确示例
?page=1&pageSize=10
?searchTerm=developer
?sortBy=createdAt&sortOrder=desc

# ❌ 错误示例
?page=1&page_size=10
?term=developer
?sort=created_at&order=desc
```

## 4. 组件命名规范

### 4.1 组件名称

- **使用 PascalCase**
- **有意义的名称**
- **避免缩写**
- **以类型结尾（如 `Button`, `Card`, `Form`）**

```typescript
// ✅ 正确示例
const UserProfile = () => {}
const JobListingsCard = () => {}
const ResumeEditorActions = () => {}
const ApiKeysForm = () => {}

// ❌ 错误示例
const userProfile = () => {}
const JobCard = () => {}
const ResumeActions = () => {}
const APIForm = () => {}
```

### 4.2 组件属性命名

- **使用 camelCase**
- **描述性名称**
- **事件处理函数以 `on` 开头**
- **布尔值属性以 `is` 或 `has` 开头**

```typescript
// ✅ 正确示例
interface ButtonProps {
  onClick: () => void;
  isLoading: boolean;
  hasIcon: boolean;
  variant: 'default' | 'outline';
}

// ❌ 错误示例
interface ButtonProps {
  onclick: () => void;
  loading: boolean;
  icon: boolean;
  Variant: 'default' | 'outline';
}
```

## 5. 变量和函数命名规范

### 5.1 变量命名

- **使用 camelCase**
- **描述性名称**
- **避免缩写**
- **布尔值以 `is` 或 `has` 开头**

```typescript
// ✅ 正确示例
const userName = 'John Doe';
const isLoading = false;
const hasPermission = true;
const jobListings = [];

// ❌ 错误示例
const user_name = 'John Doe';
const loading = false;
const permission = true;
const jobs = [];
```

### 5.2 函数命名

- **使用 camelCase**
- **以动词开头**
- **描述性行为**
- **异步函数以 `get`、`fetch`、`create`、`update`、`delete` 开头**

```typescript
// ✅ 正确示例
const getUserProfile = () => {}
const fetchJobListings = () => {}
const createResume = () => {}
const updateSettings = () => {}
const deleteJob = () => {}

// ❌ 错误示例
const userProfile = () => {}
const getJobs = () => {}
const makeResume = () => {}
const settingsUpdate = () => {}
const removeJob = () => {}
```

### 5.3 常量命名

- **使用 UPPER_SNAKE_CASE**
- **描述性名称**

```typescript
// ✅ 正确示例
const API_BASE_URL = 'https://api.snapoffer.com';
const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
const DEFAULT_PAGE_SIZE = 20;

// ❌ 错误示例
const apiUrl = 'https://api.snapoffer.com';
const maxFileSize = 10 * 1024 * 1024;
const defaultPageSize = 20;
```

## 6. CSS类名命名规范

### 6.1 通用类名

- **使用 kebab-case**
- **小写字母**
- **遵循 Tailwind CSS 约定**

```typescript
// ✅ 正确示例
className="user-profile"
className="job-listings-card"
className="resume-editor-actions"

// ❌ 错误示例
className="userProfile"
className="jobListingsCard"
className="resumeEditorActions"
```

### 6.2 状态类名

- **使用状态前缀**
- **描述性名称**

```typescript
// ✅ 正确示例
className="is-loading"
className="has-error"
className="is-active"
className="is-disabled"

// ❌ 错误示例
className="loading"
className="error"
className="active"
className="disabled"
```

## 7. 类型定义命名规范

### 7.1 接口命名

- **使用 PascalCase**
- **以 `I` 开头（可选）**
- **描述性名称**

```typescript
// ✅ 正确示例
interface UserProfile {
  id: string;
  name: string;
  email: string;
}

interface JobListing {
  id: string;
  title: string;
  company: string;
}

// ❌ 错误示例
interface userProfile {
  id: string;
  name: string;
  email: string;
}

interface job_listing {
  id: string;
  title: string;
  company: string;
}
```

### 7.2 类型别名命名

- **使用 PascalCase**
- **以 `T` 开头（可选）**
- **描述性名称**

```typescript
// ✅ 正确示例
type UserId = string;
type JobStatus = 'active' | 'inactive' | 'archived';
type ResumeData = {
  id: string;
  content: string;
};

// ❌ 错误示例
type userId = string;
type job_status = 'active' | 'inactive' | 'archived';
type resume_data = {
  id: string;
  content: string;
};
```

## 8. 枚举命名规范

### 8.1 枚举名称

- **使用 PascalCase**
- **描述性名称**

```typescript
// ✅ 正确示例
enum JobStatus {
  Active = 'active',
  Inactive = 'inactive',
  Archived = 'archived',
}

enum UserRole {
  Admin = 'admin',
  User = 'user',
  Guest = 'guest',
}

// ❌ 错误示例
enum jobStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  ARCHIVED = 'archived',
}

enum user_role {
  ADMIN = 'admin',
  USER = 'user',
  GUEST = 'guest',
}
```

## 9. 工具约定

### 9.1 ESLint 配置

项目已配置 ESLint 来强制执行大多数命名规范。运行以下命令检查代码：

```bash
npm run lint
```

### 9.2 Prettier 配置

项目使用 Prettier 来格式化代码，确保一致的代码风格：

```bash
npm run format
```

### 9.3 TypeScript 配置

TypeScript 配置强制执行类型检查，确保类型定义的正确性：

```bash
npx tsc --noEmit
```

## 10. 最佳实践

### 10.1 一致性

- **在整个项目中保持一致的命名风格**
- **遵循现有的代码模式**
- **在团队中沟通命名决策**

### 10.2 可读性

- **使用有意义的名称**
- **避免过长的名称**
- **避免不必要的缩写**

### 10.3 可维护性

- **选择反映用例的名称**
- **避免使用通用名称**
- **考虑未来的扩展性**

## 11. 迁移指南

### 11.1 重命名文件

如果需要重命名文件，请确保：

1. **更新所有导入语句**
2. **更新所有引用**
3. **检查路由配置**
4. **运行测试确保功能正常**

### 11.2 重命名目录

如果需要重命名目录，请确保：

1. **更新所有路径引用**
2. **更新导入语句**
3. **检查路由配置**
4. **运行测试确保功能正常**

## 12. 参考资源

- [Next.js 文档](https://nextjs.org/docs)
- [React 文档](https://react.dev)
- [TypeScript 文档](https://www.typescriptlang.org/docs)
- [Tailwind CSS 文档](https://tailwindcss.com/docs)
- [ESLint 文档](https://eslint.org/docs)

## 13. 常见问题

### 13.1 Q: 为什么使用 kebab-case 而不是 camelCase？

A: kebab-case 在文件系统中更友好，避免了大小写敏感性问题，并且与 URL 路径保持一致。

### 13.2 Q: 组件名称为什么使用 PascalCase？

A: PascalCase 是 React 组件的标准约定，便于区分组件和普通变量。

### 13.3 Q: 如何处理已有代码的不规范命名？

A: 逐步重构，优先处理新代码，在重构旧代码时确保不影响功能。

---

## 版本历史

- **v1.0.0** (2024-01-01) - 初始版本
- **v1.1.0** (2024-01-15) - 添加路由参数规范
- **v1.2.0** (2024-02-01) - 添加测试文件规范

## 维护者

- SnapOffer 开发团队
- 架构师评审委员会