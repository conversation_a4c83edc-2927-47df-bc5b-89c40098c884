# Hero 岗位输入功能 - 技术设计文档

## 概述

Hero 岗位输入功能通过扩展现有的 `InlineJobComposer` 组件，在落地页 Hero 区域提供岗位描述输入能力。该设计基于组件复用原则，最小化代码重复，同时支持认证状态感知的用户流程。

### 设计原则
- **组件复用**: 最大化利用现有的 `InlineJobComposer` 组件
- **渐进增强**: 通过 `heroMode` 属性扩展功能，不破坏现有实现
- **状态一致性**: 统一的认证检查和数据流管理
- **性能优先**: 智能缓存和错误处理机制

## 架构设计

### 整体架构图

```mermaid
graph TD
    A[用户访问 Hero 区域] --> B[InlineJobComposer heroMode=true]
    B --> C{认证状态检查}
    C -->|已登录| D[AI 格式化岗位]
    C -->|未登录| E[安全缓存数据]
    E --> F[触发登录对话框]
    F --> G[用户完成登录]
    G --> H[恢复缓存数据]
    H --> D
    D --> I[创建岗位记录]
    I --> J[跳转到 Jobs 页面]
    J --> K[自动打开岗位详情]
    
    L[网络错误处理] --> M[数据缓存到 sessionStorage]
    M --> N[用户反馈和重试机制]
```

### 数据流架构

```mermaid
sequenceDiagram
    participant U as 用户
    participant H as Hero组件
    participant JC as InlineJobComposer
    participant AC as 认证检查
    participant CS as 缓存服务
    participant AI as AI处理
    participant DB as 数据库
    participant JP as Jobs页面

    U->>H: 输入岗位描述
    H->>JC: 提交数据 (heroMode=true)
    JC->>AC: 检查用户认证状态
    
    alt 用户已登录
        AC-->>JC: 返回用户信息
        JC->>AI: 格式化岗位描述
        AI-->>JC: 返回格式化数据
        JC->>DB: 创建岗位记录
        DB-->>JC: 返回岗位ID
        JC->>JP: 跳转并传递岗位ID
    else 用户未登录
        AC-->>JC: 未认证状态
        JC->>CS: 安全缓存岗位数据
        JC->>U: 触发登录对话框
        U->>JC: 完成登录
        JC->>CS: 恢复缓存数据
        JC->>AI: 继续处理流程
    end
```

## 组件设计

### 1. InlineJobComposer 组件扩展

#### 接口扩展
```typescript
interface InlineJobComposerProps {
  onJobAdded?: (job: Job) => void;
  heroMode?: boolean; // 新增：Hero 模式标志
  onAuthRequired?: () => void; // 新增：认证需求回调
  redirectAfterCreate?: boolean; // 新增：创建后是否跳转
}
```

#### 功能增强点
1. **样式适配**: `heroMode` 时使用静态布局替代固定定位
2. **认证检查**: 提交前进行用户认证状态验证
3. **数据缓存**: 未登录用户的数据安全存储
4. **跳转控制**: 成功创建后的路由跳转逻辑

### 2. Hero 组件重构

#### 新布局结构
```typescript
// 新的 Hero 组件结构
export function Hero() {
  return (
    <section className="min-h-[90vh] flex flex-col justify-center items-center px-4 py-8">
      {/* 顶部标题区域 */}
      <div className="text-center max-w-4xl mx-auto mb-12">
        <h1>标题内容</h1>
        <p>描述内容</p>
      </div>

      {/* 中间输入框区域 */}
      <div className="w-full max-w-3xl mx-auto mb-12">
        <HeroJobInput />
      </div>

      {/* 底部辅助信息 */}
      <div className="text-center">
        <div>功能标签</div>
        <AuthDialog>注册按钮</AuthDialog>
      </div>
    </section>
  );
}
```

#### 关键设计决策
- **去除简历预览**: 简化布局，聚焦核心功能
- **中心化布局**: 输入框居中放置，符合用户习惯
- **单屏约束**: 保持 `min-h-[90vh]` 确保一屏显示

### 3. 认证流程处理组件

#### 客户端认证检查
```typescript
async function checkAuthenticationStatus(): Promise<AuthResult> {
  try {
    const supabase = createClient();
    const { data: { user }, error } = await supabase.auth.getUser();
    
    if (error) throw error;
    
    return {
      isAuthenticated: !!user,
      user: user || null,
      error: null
    };
  } catch (error) {
    return {
      isAuthenticated: false,
      user: null,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}
```

### 4. 安全缓存服务

#### 数据结构设计
```typescript
interface CachedJobData {
  description: string;
  timestamp: number;
  sessionId: string;
  checksum: string;
  source: 'hero' | 'jobs';
}

interface CacheService {
  store(data: string): void;
  retrieve(): CachedJobData | null;
  clear(): void;
  isValid(cached: CachedJobData): boolean;
}
```

#### 安全实现
```typescript
class JobDataCache implements CacheService {
  private readonly CACHE_KEY = 'pending-job-data';
  private readonly CACHE_TIMEOUT = 15 * 60 * 1000; // 15分钟

  store(description: string): void {
    const data: CachedJobData = {
      description,
      timestamp: Date.now(),
      sessionId: crypto.randomUUID(),
      checksum: this.generateChecksum(description),
      source: 'hero'
    };
    
    const encrypted = btoa(JSON.stringify(data));
    sessionStorage.setItem(this.CACHE_KEY, encrypted);
    
    // 设置自动清理
    setTimeout(() => this.clear(), this.CACHE_TIMEOUT);
  }

  retrieve(): CachedJobData | null {
    try {
      const encrypted = sessionStorage.getItem(this.CACHE_KEY);
      if (!encrypted) return null;
      
      const data = JSON.parse(atob(encrypted)) as CachedJobData;
      return this.isValid(data) ? data : null;
    } catch {
      this.clear();
      return null;
    }
  }

  private generateChecksum(data: string): string {
    // 简单校验和实现
    return btoa(data).slice(-8);
  }

  private isValid(cached: CachedJobData): boolean {
    const now = Date.now();
    const isNotExpired = (now - cached.timestamp) < this.CACHE_TIMEOUT;
    const checksumValid = this.generateChecksum(cached.description) === cached.checksum;
    
    return isNotExpired && checksumValid;
  }
}
```

## 数据模型

### 1. 组件状态管理
```typescript
interface HeroJobInputState {
  // 输入状态
  value: string;
  isValid: boolean;
  errorMessage: string;
  
  // 处理状态
  isFormatting: boolean;
  isCreating: boolean;
  isCheckingAuth: boolean;
  
  // 认证状态
  isAuthenticated: boolean;
  user: User | null;
  
  // 缓存状态
  hasCachedData: boolean;
  cacheTimestamp: number | null;
}
```

### 2. 路由参数设计
```typescript
interface JobsPageParams {
  autoOpenJobId?: string; // 自动打开的岗位ID
  source?: 'hero' | 'direct'; // 来源标识
}

// URL 示例：/jobs?autoOpenJobId=123&source=hero
```

### 3. 错误处理模型
```typescript
interface ErrorState {
  type: 'validation' | 'network' | 'auth' | 'server';
  message: string;
  code?: string;
  retryable: boolean;
  actionText?: string;
}
```

## 接口设计

### 1. 组件接口
```typescript
// InlineJobComposer 扩展接口
interface InlineJobComposerProps {
  onJobAdded?: (job: Job) => void;
  heroMode?: boolean;
  onAuthRequired?: () => void;
  redirectAfterCreate?: boolean;
}

// Hero 子组件接口
interface HeroJobInputProps {
  onSuccess?: (jobId: string) => void;
  onError?: (error: ErrorState) => void;
}
```

### 2. 服务接口
```typescript
// 认证检查服务
interface AuthService {
  checkStatus(): Promise<AuthResult>;
  requireAuth(): Promise<void>;
}

// 缓存服务接口
interface CacheService {
  store(data: string): void;
  retrieve(): CachedJobData | null;
  clear(): void;
}

// 路由服务接口
interface NavigationService {
  redirectToJobs(jobId: string, source?: string): void;
  preserveScrollPosition(): void;
}
```

## 错误处理策略

### 1. 错误分类和处理
```typescript
enum ErrorType {
  VALIDATION = 'validation',      // 输入验证错误
  NETWORK = 'network',           // 网络连接错误
  AUTH = 'auth',                 // 认证相关错误
  SERVER = 'server',             // 服务器处理错误
  CACHE = 'cache'               // 缓存相关错误
}

interface ErrorHandler {
  handle(error: Error, type: ErrorType): ErrorResponse;
}
```

### 2. 重试机制
```typescript
interface RetryConfig {
  maxAttempts: number;
  backoffMs: number;
  exponential: boolean;
}

class JobSubmissionService {
  async submitWithRetry(
    data: string, 
    config: RetryConfig = { maxAttempts: 3, backoffMs: 1000, exponential: true }
  ): Promise<Job> {
    // 实现指数退避重试逻辑
  }
}
```

### 3. 网络状态处理
```typescript
class NetworkStatusHandler {
  private isOnline: boolean = navigator.onLine;
  
  constructor() {
    window.addEventListener('online', this.handleOnline);
    window.addEventListener('offline', this.handleOffline);
  }
  
  private handleOffline = () => {
    this.isOnline = false;
    // 触发离线模式，启用缓存
  };
  
  private handleOnline = () => {
    this.isOnline = true;
    // 尝试处理缓存的数据
  };
}
```

## 测试策略

### 1. 单元测试
```typescript
describe('InlineJobComposer Hero Mode', () => {
  it('should render with hero-specific styling when heroMode is true', () => {
    // 测试 heroMode 样式应用
  });
  
  it('should handle authentication check on submit', async () => {
    // 测试认证检查逻辑
  });
  
  it('should cache data when user is not authenticated', async () => {
    // 测试数据缓存功能
  });
  
  it('should restore cached data after authentication', async () => {
    // 测试数据恢复功能
  });
});

describe('JobDataCache', () => {
  it('should store and retrieve data correctly', () => {
    // 测试缓存存储和检索
  });
  
  it('should validate data integrity', () => {
    // 测试数据完整性校验
  });
  
  it('should auto-expire old cache entries', () => {
    // 测试自动过期机制
  });
});
```

### 2. 集成测试
```typescript
describe('Hero to Jobs Flow', () => {
  it('should complete end-to-end flow for authenticated user', async () => {
    // 测试已登录用户的完整流程
  });
  
  it('should handle authentication flow for new users', async () => {
    // 测试未登录用户的认证流程
  });
  
  it('should handle network failures gracefully', async () => {
    // 测试网络故障处理
  });
});
```

### 3. 用户体验测试
- **响应性测试**: 确保在标准设备上100ms内渲染
- **可访问性测试**: 验证屏幕阅读器兼容性
- **移动端测试**: 确保触摸优化和响应式布局
- **性能测试**: 验证缓存机制不影响页面性能

## 部署和配置

### 1. 环境变量
```bash
# 现有环境变量继续使用
NEXT_PUBLIC_SUPABASE_URL=
NEXT_PUBLIC_SUPABASE_ANON_KEY=
SUPABASE_SERVICE_ROLE_KEY=

# 可选的功能开关
FEATURE_HERO_JOB_INPUT=true
CACHE_TIMEOUT_MINUTES=15
```

### 2. 功能开关
```typescript
interface FeatureFlags {
  heroJobInput: boolean;
  extendedCaching: boolean;
  advancedRetry: boolean;
}

const featureFlags: FeatureFlags = {
  heroJobInput: process.env.FEATURE_HERO_JOB_INPUT === 'true',
  extendedCaching: true,
  advancedRetry: true
};
```

### 3. 监控和分析
- **用户转化率**: Hero 输入到岗位创建的转化
- **错误率**: 各类错误的发生频率
- **性能指标**: 页面加载和交互响应时间
- **用户行为**: 输入框使用模式和放弃率

## 安全考量

### 1. 数据安全
- 岗位描述可能包含敏感的公司信息
- 使用 Base64 编码和校验和防止数据篡改
- 设置合理的缓存过期时间（15分钟）

### 2. 会话安全
- 使用唯一的会话标识符防止跨会话数据污染
- 自动清理过期的缓存数据
- 在用户登出时清除所有缓存

### 3. 输入安全
- 继续使用现有的 `jobDescriptionInputSchema` 验证
- 服务端重新验证所有用户输入
- 防止 XSS 和注入攻击

这个设计文档基于现有的代码库架构，最大化复用现有组件和逻辑，同时引入必要的新功能来支持 Hero 区域的岗位输入需求。设计重点在于保持代码的可维护性和一致性，同时提供良好的用户体验。
