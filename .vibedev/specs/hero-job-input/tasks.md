# Hero 岗位输入功能 - 实施任务列表

## 任务概述

基于需求文档和设计文档，将 Hero 岗位输入功能分解为可执行的代码实施任务。每个任务都是增量式的，可以独立测试和验证。

## 实施任务

### 1. 核心组件扩展

- [ ] **1.1 扩展 InlineJobComposer 组件接口**
  - 修改 `src/components/jobs/inline-job-composer.tsx` 文件
  - 添加 `heroMode`、`onAuthRequired`、`redirectAfterCreate` 属性到接口
  - 更新组件 JSDoc 注释说明新属性用途
  - 对应需求：5.1 (使用现有 InlineJobComposer 组件)

- [ ] **1.2 实现 heroMode 样式适配逻辑**
  - 在 InlineJobComposer 组件中添加条件样式渲染
  - 当 `heroMode=true` 时使用静态布局替代固定定位
  - 调整容器样式、间距和响应式行为
  - 创建针对 Hero 模式的 CSS 类名
  - 对应需求：4.1 (保持单屏视口高度)、4.2 (移动设备适配)

- [ ] **1.3 添加客户端认证状态检查功能**
  - 在 InlineJobComposer 的 `handleSubmit` 方法中集成认证检查
  - 使用现有的 `createClient` 从 `@/utils/supabase/client` 检查用户状态
  - 添加超时处理和网络错误捕获机制
  - 对应需求：2.1、2.4 (认证状态处理和网络错误处理)

### 2. 安全缓存系统

- [ ] **2.1 创建岗位数据缓存服务**
  - 创建新文件 `src/utils/job-data-cache.ts`
  - 实现 `JobDataCache` 类，包含 store、retrieve、clear、isValid 方法
  - 添加数据加密、校验和生成、会话隔离功能
  - 实现15分钟自动过期机制
  - 对应需求：7.1、7.2 (数据编码和会话标识符)

- [ ] **2.2 集成缓存服务到 InlineJobComposer**
  - 在未认证用户提交时调用缓存服务存储数据
  - 实现用户认证后的数据恢复逻辑
  - 添加缓存数据的完整性验证
  - 处理缓存过期和清理场景
  - 对应需求：2.2、2.3 (缓存数据和恢复工作流程)

- [ ] **2.3 实现网络状态感知缓存**
  - 添加网络状态监听器 (online/offline 事件)
  - 网络离线时自动启用缓存模式
  - 网络恢复时提供处理缓存数据选项
  - 添加用户友好的网络状态反馈
  - 对应需求：6.4 (网络超时处理)、8.5 (网络连接差的备用方案)

### 3. 用户认证流程

- [ ] **3.1 实现认证需求回调机制**
  - 修改 InlineJobComposer 处理 `onAuthRequired` 回调
  - 未认证时触发回调并显示适当的用户提示
  - 集成现有的 `AuthDialog` 组件触发登录流程
  - 对应需求：2.2 (触发认证对话框)

- [ ] **3.2 添加登录后数据恢复逻辑**
  - 在组件挂载时检查是否有缓存的岗位数据
  - 用户认证成功后自动恢复并继续处理流程
  - 实现数据恢复的确认机制和用户提示
  - 对应需求：2.3 (登录后恢复缓存数据)

### 4. 路由跳转功能

- [ ] **4.1 实现岗位创建后的跳转逻辑**
  - 修改 InlineJobComposer 的成功处理逻辑
  - 当 `redirectAfterCreate=true` 时执行页面跳转
  - 使用 Next.js `useRouter` 跳转到 Jobs 页面并传递岗位ID
  - 对应需求：3.1 (跳转到岗位页面)

- [ ] **4.2 修改 Jobs 页面支持自动打开指定岗位**
  - 修改 `src/app/(dashboard)/jobs/page.tsx` 文件
  - 读取 URL 参数中的 `autoOpenJobId` 和 `source` 参数
  - 自动触发指定岗位的详情模态框
  - 对应需求：3.2 (自动打开岗位详情模态框)

- [ ] **4.3 实现状态恢复和URL参数处理**
  - 在 Jobs 页面组件中处理来自 Hero 的跳转参数
  - 确保岗位详情正确显示并标记来源
  - 处理无效岗位ID的错误情况
  - 对应需求：3.5 (自动显示岗位详情)

### 5. Hero 组件重构

- [ ] **5.1 重构 Hero 组件布局结构**
  - 修改 `src/components/landing/Hero.tsx` 文件
  - 移除现有的简历预览组件 (右侧内容)
  - 重新设计为居中垂直布局：标题区域 → 输入区域 → 辅助信息
  - 确保保持 `min-h-[90vh]` 单屏约束
  - 对应需求：4.1 (单屏视口高度)

- [ ] **5.2 集成 InlineJobComposer 到 Hero**
  - 在 Hero 中央区域添加 InlineJobComposer 组件
  - 设置 `heroMode={true}` 和 `redirectAfterCreate={true}` 属性
  - 添加合适的标题和说明文字
  - 确保响应式设计和移动端适配
  - 对应需求：1.1 (Hero 区域显示输入区域)、4.2 (移动设备适配)

- [ ] **5.3 添加 Hero 特定的样式和交互**
  - 创建 Hero 输入区域的介绍文字和视觉提示
  - 添加功能标签展示 (智能求职、岗位分析等)
  - 保持与现有 AuthDialog 的集成
  - 对应需求：1.1 (显著展示输入区域)

### 6. 错误处理和用户反馈

- [ ] **6.1 实现分类错误处理系统**
  - 创建 `src/utils/error-handler.ts` 文件
  - 定义错误类型枚举和处理接口
  - 实现不同错误类型的处理策略 (validation, network, auth, server)
  - 集成到 InlineJobComposer 的错误处理流程
  - 对应需求：4.5 (用户友好错误消息)

- [ ] **6.2 添加重试机制和网络故障处理**
  - 实现指数退避重试逻辑
  - 添加网络超时检测和自动重试
  - 提供手动重试选项和用户反馈
  - 对应需求：6.4 (备用缓存在5秒内启动)

- [ ] **6.3 优化加载状态和用户反馈**
  - 改进现有的加载指示器显示逻辑
  - 添加不同处理阶段的具体状态提示 (验证、格式化、创建)
  - 实现更详细的进度反馈
  - 对应需求：4.3、4.4 (视觉反馈和加载指示器)

### 7. 测试实施

- [ ] **7.1 创建 InlineJobComposer heroMode 的单元测试**
  - 创建测试文件 `src/components/jobs/__tests__/inline-job-composer-hero.test.ts`
  - 测试 heroMode 样式应用和属性传递
  - 测试认证检查和缓存逻辑
  - 测试错误处理和重试机制
  - 对应需求：所有功能性需求的验证

- [ ] **7.2 创建缓存服务的单元测试**
  - 创建测试文件 `src/utils/__tests__/job-data-cache.test.ts`
  - 测试数据存储、检索、验证功能
  - 测试过期机制和数据清理
  - 测试数据完整性和安全性
  - 对应需求：7.1、7.2、7.4 (安全要求验证)

- [ ] **7.3 创建端到端流程的集成测试**
  - 创建测试文件 `src/__tests__/hero-to-jobs-flow.test.ts`
  - 测试已认证用户的完整流程
  - 测试未认证用户的认证和恢复流程
  - 测试网络故障和错误恢复场景
  - 对应需求：3.1-3.5 (页面跳转和状态管理)

### 8. 性能优化和可访问性

- [ ] **8.1 实现性能优化措施**
  - 添加组件 memo 化以减少不必要的重渲染
  - 优化缓存操作以避免影响页面性能
  - 实现组件懒加载（如果需要）
  - 对应需求：6.1、6.3 (性能要求)

- [ ] **8.2 添加可访问性支持**
  - 为所有交互元素添加适当的 ARIA 标签
  - 确保键盘导航的完整性
  - 添加屏幕阅读器支持
  - 测试焦点管理和视觉指示器
  - 对应需求：8.4 (屏幕阅读器支持)

- [ ] **8.3 实现浏览器兼容性支持**
  - 测试在目标浏览器 (Chrome 90+, Firefox 88+, Safari 14+) 的功能
  - 添加必要的 polyfill 或替代实现
  - 实现 JavaScript 禁用时的优雅降级
  - 对应需求：8.1、8.3 (兼容性要求)

### 9. 最终集成和验证

- [ ] **9.1 完整功能集成验证**
  - 验证所有组件正确集成和协作
  - 测试完整的用户流程 (Hero → 认证 → Jobs)
  - 确保没有遗留的未连接代码或功能
  - 对应需求：所有需求的综合验证

- [ ] **9.2 代码质量和规范检查**
  - 确保代码符合项目的 TypeScript 和 ESLint 规范
  - 添加完整的 JSDoc 注释和类型定义
  - 验证遵循项目的设计令牌系统
  - 对应需求：5.4、5.5 (设计令牌和现有工具使用)

## 任务执行说明

- 每个任务都应该是独立可测试的
- 建议按照编号顺序执行，确保依赖关系正确
- 每完成一个任务都应该进行测试验证
- 如遇到阻塞问题，可以参考需求文档和设计文档
- 所有新增代码都应该包含适当的类型定义和错误处理
