# 需求文档：认证登录页面清理

## 简介

本功能旨在通过移除冗余的认证页面并标准化所有认证交互为基于模态框的方式来简化 SnapOffer 的认证系统。此次清理将删除 `/auth/login` 页面及其独有的着陆页组件，同时确保所有认证流程继续无缝工作。这符合 SnapOffer 的极简设计理念并提高代码可维护性。

## 功能需求

### 1. 页面和组件清理
**用户故事**：作为开发者，我希望移除冗余的认证页面和组件，以便代码库更加清洁和易于维护。

**验收标准**：
1. 当系统部署后，`/auth/login` 页面应当完全移除（返回 404）
2. 当清理完成后，应当删除以下 7 个着陆页组件：
   - `src/components/landing/mock-resume.tsx`
   - `src/components/landing/mock-resume-mobile.tsx`
   - `src/components/landing/benefits-list.tsx`
   - `src/components/landing/action-buttons.tsx`
   - `src/components/landing/hero-video-section.tsx`
   - `src/components/landing/model-showcase.tsx`
   - `src/components/landing/pricing-section.tsx`
3. 当组件被删除后，系统的其他部分不应当引用这些组件
4. 当清理完成后，构建过程应当成功且无错误

### 2. 认证模态框标准化
**用户故事**：作为用户，我希望所有认证交互都使用一致的模态框界面，以便在整个平台上获得无缝体验。

**验收标准**：
1. 当我需要在任何页面登录时，应当看到 `AuthDialog` 模态框
2. 当我需要在任何页面注册时，应当看到带有注册标签页的 `AuthDialog` 模态框
3. 当我点击价格方案的 CTA 按钮时，应当打开认证模态框（而不是重定向到不存在的页面）
4. 当我已经登录时，导航应当显示"工作台"而不是登录选项
5. 当认证成功时，应当重定向到 `/home`

### 3. 深链认证支持
**用户故事**：作为用户，我希望能够通过深链接访问认证功能，以便可以收藏或分享特定的认证状态。

**验收标准**：
1. 当我访问 `/?auth=login` 时，应当打开带有登录标签页激活状态的认证模态框
2. 当我访问 `/?auth=signup` 时，应当打开带有注册标签页激活状态的认证模态框
3. 当模态框通过深链接打开时，URL 查询参数应当在打开后被清除
4. 当我关闭通过深链接打开的模态框时，应当停留在首页

### 4. 认证流程保护
**用户故事**：作为用户，我希望所有现有的认证流程继续工作，以便可以完成邮箱验证、密码重置和 OAuth 登录而不出现问题。

**验收标准**：
1. 当我点击邮箱验证链接时，`/auth/confirm` 应当在无需认证的情况下可访问
2. 当我使用 OAuth 登录（Google/GitHub）时，`/auth/callback` 应当在无需认证的情况下可访问
3. 当我重置密码时，`/auth/reset-password` 应当在无需认证的情况下可访问
4. 当我点击密码重置链接时，`/auth/update-password` 应当在无需认证的情况下可访问
5. 当我完成任何这些流程时，应当被适当重定向

## 技术需求

### 5. 中间件配置
**用户故事**：作为开发者，我希望中间件能够正确处理认证路由，以便关键的认证流程不被阻塞。

**验收标准**：
1. 当未认证用户访问 `/auth/callback` 时，不应当被重定向到首页
2. 当未认证用户访问 `/auth/confirm` 时，不应当被重定向到首页
3. 当未认证用户访问 `/auth/reset-password` 时，不应当被重定向到首页
4. 当未认证用户访问 `/auth/update-password` 时，不应当被重定向到首页
5. 当未认证用户访问其他受保护路由时，应当被重定向到首页

### 6. 文档更新
**用户故事**：作为开发者，我希望项目文档反映当前的代码库结构，以便未来的开发不会被过时的示例混淆。

**验收标准**：
1. 当文档更新时，`docs/NAMING_CONVENTIONS.md` 不应当引用已删除的认证页面
2. 当文档更新时，所有示例应当使用当前的文件结构
3. 当文档审查时，应当准确反映新的认证流程

## 质量需求

### 7. 代码质量和类型安全
**用户故事**：作为开发者，我希望代码库保持高质量标准，以便系统保持可维护性和可靠性。

**验收标准**：
1. 当清理完成时，所有 TypeScript 编译应当成功且无错误
2. 当清理完成时，ESLint 检查应当通过且无警告
3. 当组件被移除时，不应当存在死引用或死代码
4. 当代码构建时，不应当有未使用依赖的警告

### 8. 用户体验一致性
**用户故事**：作为用户，我希望认证交互遵循平台的设计原则，以便界面感觉连贯和专业。

**验收标准**：
1. 当认证模态框打开时，应当遵循 SnapOffer 的设计令牌系统
2. 当执行认证操作时，应当使用既定的颜色调色板（85% 黑白、5% 灰色、10% 品牌蓝）
3. 当显示模态框时，应当一致地使用 Shadcn UI 组件
4. 当发生动画时，应当遵循项目的动画标准

## 部署和回滚需求

### 9. 安全部署
**用户故事**：作为开发者，我希望安全地部署认证变更，以便用户不受功能损坏的影响。

**验收标准**：
1. 当功能部署时，所有现有用户会话应当保持有效
2. 当功能部署时，用户数据不应当受到影响
3. 当测试认证流程时，所有关键路径应当正常工作
4. 当需要回滚时，应当能够通过 Git 完全恢复到之前的状态

### 10. 性能影响
**用户故事**：作为用户，我希望认证系统性能良好，以便登录和注册快速且响应迅速。

**验收标准**：
1. 当组件被移除时，bundle 大小应当减少
2. 当首页加载时，不应当加载不必要的认证页面组件
3. 当认证模态框打开时，应当快速加载且不阻塞主线程
4. 当导航页面时，与当前实现相比不应当有性能回退

## 安全需求

### 11. 认证安全
**用户故事**：作为用户，我希望我的认证保持安全，以便我的账户和数据受到保护。

**验收标准**：
1. 当认证流程被修改时，所有安全措施应当保持完整
2. 当中间件变更时，受保护的路由应当仍然需要认证
3. 当处理认证令牌时，应当继续安全地处理它们
4. 当访问密码重置流程时，应当维持当前的安全验证

## 边缘情况和错误处理

### 12. 错误处理
**用户故事**：作为用户，我希望在认证错误发生时得到清晰的反馈，以便我能够理解和解决问题。

**验收标准**：
1. 当认证失败时，应当在模态框中看到适当的错误消息
2. 当认证过程中发生网络错误时，应当看到有用的重试选项
3. 当深链接参数无效时，应当看到默认首页且无错误
4. 当我直接访问已删除的路由时，应当看到合适的 404 页面

### 13. 浏览器兼容性
**用户故事**：作为用户，我希望认证功能在不同浏览器中都能工作，以便无论我选择哪种浏览器都能访问平台。

**验收标准**：
1. 当我使用认证模态框时，应当在 Chrome、Firefox、Safari 和 Edge 中工作
2. 当我使用键盘导航时，模态框应当完全可访问
3. 当我使用屏幕阅读器时，认证流程应当被正确播报
4. 当 JavaScript 被禁用时，应当看到适当的回退消息
