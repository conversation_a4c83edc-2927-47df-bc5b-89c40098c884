# SmartNavButton 组件设计文档

## 概述

SmartNavButton 是一个极简的智能导航按钮组件，用于在导航栏中根据用户认证状态显示不同的按钮文本。该组件将作为AppHeader的子组件使用，提供简洁的用户状态显示和导航功能。

## 架构设计

### 组件定位
- **父组件**: AppHeader (src/components/layout/app-header.tsx)
- **组件类型**: Client Component (需要状态管理)
- **位置**: 导航栏右侧区域

### 技术栈
- **框架**: Next.js 15 + React 19 + TypeScript
- **状态管理**: React useState + useEffect
- **认证**: Supabase Auth
- **样式**: Tailwind CSS + 设计令牌系统
- **UI组件**: shadcn/ui Button

## 组件接口设计

### Props 接口
```typescript
interface SmartNavButtonProps {
  className?: string;
  variant?: 'default' | 'ghost' | 'outline';
  size?: 'default' | 'sm' | 'lg';
}
```

### 状态管理
```typescript
// 组件内部状态
interface SmartNavButtonState {
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}
```

## 组件实现设计

### 核心逻辑流程
```mermaid
graph TD
    A[组件挂载] --> B[检查认证状态]
    B --> C{用户已登录?}
    C -->|是| D[显示"工作台"按钮]
    C -->|否| E[显示"登录"按钮]
    D --> F[点击跳转到 /home]
    E --> G[点击打开AuthDialog]
    B --> H[监听认证状态变化]
    H --> C
```

### 组件结构
```typescript
'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { designTokens } from '@/lib/design-tokens';
import { createClient } from '@/utils/supabase/client';
import Link from 'next/link';
import { AuthDialog } from '@/components/auth/auth-dialog';
import { cn } from '@/lib/utils';

export function SmartNavButton({ 
  className = '',
  variant = 'ghost',
  size = 'default'
}: SmartNavButtonProps) {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showAuthDialog, setShowAuthDialog] = useState(false);

  useEffect(() => {
    checkAuthStatus();
    
    // 监听认证状态变化
    const { data: { subscription } } = createClient().auth.onAuthStateChange(
      (event, session) => {
        setIsAuthenticated(!!session);
        setIsLoading(false);
      }
    );

    return () => subscription.unsubscribe();
  }, []);

  const checkAuthStatus = async () => {
    try {
      const { data: { session } } = await createClient().auth.getSession();
      setIsAuthenticated(!!session);
    } catch (err) {
      setError('认证状态检查失败');
    } finally {
      setIsLoading(false);
    }
  };

  const handleLoginClick = () => {
    setShowAuthDialog(true);
  };

  if (isLoading) {
    return (
      <Button 
        variant={variant} 
        size={size}
        className={className}
        disabled
      >
        <span className="animate-pulse">加载中</span>
      </Button>
    );
  }

  if (error) {
    return (
      <Button 
        variant={variant} 
        size={size}
        className={className}
        disabled
      >
        错误
      </Button>
    );
  }

  if (isAuthenticated) {
    return (
      <Link href="/home" className="inline-block">
        <Button 
          variant={variant} 
          size={size}
          className={cn(
            className,
            "text-gray-700 hover:text-blue-500", // 使用设计令牌颜色
            "transition-colors duration-200"     // 平滑过渡效果
          )}
        >
          工作台
        </Button>
      </Link>
    );
  }

  return (
    <>
      <Button 
        variant={variant} 
        size={size}
        className={cn(
          className,
          "text-gray-700 hover:text-blue-500", // 使用设计令牌颜色
          "transition-colors duration-200"     // 平滑过渡效果
        )}
        onClick={handleLoginClick}
      >
        登录
      </Button>
      <AuthDialog open={showAuthDialog} onOpenChange={setShowAuthDialog} />
    </>
  );
}
```

## 样式设计

### 设计令牌使用
```typescript
// 颜色使用
colors: {
  text: designTokens.colors.gray[700],      // 主要文字颜色
  hover: designTokens.colors.blue[500],      // 悬停颜色（品牌蓝）
  disabled: designTokens.colors.gray[300],   // 禁用状态颜色
}

// 动画设置
animations: {
  duration: designTokens.animations.duration.normal,  // 300ms
  easing: designTokens.animations.easing.easeOut,    // ease-out
}

// 字体设置
typography: {
  fontSize: designTokens.typography.fontSize.sm,      // 14px
  fontWeight: designTokens.typography.fontWeight.medium, // 500
}
```

### Hover 效果实现
```typescript
// Hover 效果 - 字体颜色变化（非背景色）
hover:text-blue-500 // 使用品牌蓝作为悬停颜色
transition-colors duration-200 // 平滑过渡效果
```

## 集成设计

### AppHeader 集成
```typescript
// 在 AppHeader 组件中的集成位置
{/* 替换现有的工作台链接 */}
<SmartNavButton 
  variant="ghost"
  size="default"
  className="px-2 lg:px-3 py-1"
/>
```

### 认证流程集成
- **登录流程**: 点击"登录"按钮打开AuthDialog，与hero section的"开始创建"按钮效果一致
- **注册流程**: 通过AuthDialog的"创建账户"选项卡完成注册
- **社交登录**: AuthDialog内置Google OAuth支持
- **登录成功**: 自动重定向到/home页面
- **状态同步**: 通过Supabase的onAuthStateChange监听器实时更新按钮状态

### 响应式设计
```typescript
// 移动端适配
@media (max-width: 768px) {
  .smart-nav-button {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
  }
}

// 桌面端样式
@media (min-width: 769px) {
  .smart-nav-button {
    padding: 0.5rem 1.5rem;
    font-size: 1rem;
  }
}
```

## 错误处理

### 错误类型
1. **网络错误**: Supabase 连接失败
2. **认证错误**: Session 获取失败
3. **状态同步错误**: 认证状态监听失败

### 错误处理策略
```typescript
// 错误边界处理
try {
  const { data: { session } } = await createClient().auth.getSession();
  setIsAuthenticated(!!session);
} catch (err) {
  setError('认证状态检查失败');
  // 可以添加错误上报逻辑
} finally {
  setIsLoading(false);
}
```

## 性能优化

### 状态管理优化
- 使用 useEffect 清理函数避免内存泄漏
- 避免不必要的重新渲染
- 使用 React.memo 优化组件性能

### 网络请求优化
- 复用 Supabase 客户端实例
- 缓存认证状态减少重复检查
- 使用 onAuthStateChange 监听状态变化

## 测试策略

### 单元测试
```typescript
// 测试用例覆盖
describe('SmartNavButton', () => {
  it('should render "登录" when user is not authenticated', () => {
    // 测试未登录状态
  });

  it('should render "工作台" when user is authenticated', () => {
    // 测试已登录状态
  });

  it('should show loading state during authentication check', () => {
    // 测试加载状态
  });

  it('should handle authentication errors gracefully', () => {
    // 测试错误处理
  });
});
```

### 集成测试
- 测试与 AppHeader 的集成
- 测试路由跳转功能
- 测试 Supabase 认证流程

## 无障碍设计

### ARIA 属性
```typescript
<button
  aria-label={isAuthenticated ? "进入工作台" : "用户登录"}
  aria-disabled={isLoading}
  role="button"
>
  {isAuthenticated ? '工作台' : '登录'}
</button>
```

### 键盘导航
- 支持 Tab 键导航
- 支持 Enter 键激活
- 支持屏幕阅读器

## 安全考虑

### 认证安全
- 使用 Supabase 的安全认证机制
- 避免在客户端存储敏感信息
- 使用 HTTPS 进行所有通信

### 输入验证
- 验证认证状态的有效性
- 处理异常的认证状态变化
- 防止 XSS 攻击

## 部署考虑

### 环境配置
- 确保 Supabase 环境变量正确配置
- 验证路由配置的正确性
- 检查样式在生产环境中的表现

### 监控和日志
- 添加错误监控
- 记录用户认证状态变化
- 监控组件性能指标

## 总结

SmartNavButton 组件设计遵循了极简高效的原则，提供了：

1. **简洁的 API**: 只需要基本的 props 配置
2. **智能的状态管理**: 自动检测和响应认证状态变化
3. **优雅的错误处理**: 优雅地处理各种异常情况
4. **一致的设计体验**: 完全遵循项目设计令牌系统
5. **良好的性能**: 优化了状态管理和网络请求
6. **完整的测试覆盖**: 包含单元测试和集成测试

该组件可以作为导航栏的标准组件使用，为用户提供一致的认证状态显示和导航体验。