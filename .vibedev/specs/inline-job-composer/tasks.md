### Inline Job Composer — 实施任务清单

将已确认的设计分解为可执行的编码任务，采用增量、小步快跑及优先验证核心路径的方式组织。所有任务仅涉及编写/修改/测试代码。

1. [x] 创建组件文件骨架（R-1, R-7, R-8, R-10）
   - 1.1 [ ] 新建 `src/components/jobs/inline-job-composer.tsx`，导出 `InlineJobComposer` 客户端组件
   - 1.2 [ ] 搭建底部固定输入条结构（说明/输入/操作区），令牌化样式：`fixed left-0 right-0 z-30 bottom-[env(safe-area-inset-bottom)] mb-2 sm:mb-3` + `container max-w-6xl` + 面板 `bg-white border-t border-gray-200 shadow-[0_-4px_12px_rgba(0,0,0,0.06)] rounded-t-lg p-3 sm:p-4`
   - 1.3 [ ] 接入 `Textarea` 基础组件与 `Button`，不使用 `hover:bg-*` 改色；主体容器添加 `padding-bottom` 以避免被底栏遮挡（保守值或根据高度动态设置）
   - 1.4 [ ] 响应式：
      - `<md`：按钮区可换行，按钮触控区 ≥ 44px；输入 `text-sm px-3 py-2.5`
      - `md:... <lg`：按钮右对齐，面板 `md:p-4`
      - `>=lg`：保持 `container max-w-6xl` 居中与舒适间距

2. [x] 输入与自适应高度（R-2, R-3, R-8）
   - 2.1 [ ] 实现 `textareaRef` 高度测量：`style.height=auto; height=min(scrollHeight,144px)`
   - 2.2 [ ] 设置 `min-h`≈84–96px、`max-h`=144px、`overflow-y-auto`、`resize-none`
   - 2.3 [ ] 处理键盘：Enter 提交、Shift+Enter 换行并在 `requestAnimationFrame` 后自适应

3. [x] 校验与中文错误提示（R-4, R-6）
   - 3.1 [ ] 实现长度校验（<50 / >10000）并在无效时 `toast.error` 中文提示与无效态样式
   - 3.2 [ ] 加入 `aria-invalid` 与 `aria-live="polite"` 提示区域

4. [x] 接入 AI+DB 流程与状态（R-5, R-6）
   - 4.1 [ ] 读取 `localStorage('SnapOffer-default-model')` 回退默认模型
   - 4.2 [ ] 串联 `formatJobListing` → `createJob`，loading 态禁用按钮
   - 4.3 [ ] 成功后中文 `toast.success`、重置输入、`props.onJobAdded(newJob)` 回调
   - 4.4 [ ] 捕获并分类中文错误提示（API 密钥/鉴权/其他），避免敏感日志

5. [x] 页面集成与回滚点（R-1, R-5, R-9）
   - 5.1 [ ] 在 `src/app/(dashboard)/jobs/page.tsx` 引入并渲染 `InlineJobComposer onJobAdded={handleJobAdded}`
   - 5.2 [ ] 注释 `<AddJobDialog />` 调用并添加回滚说明注释（不删除文件与导出），同时清理工具栏中该按钮的布局占位，仅保留阶段筛选 `Select`
   - 5.3 [ ] 为页面根容器添加底部留白（与底栏高度+视觉上浮+安全区匹配）以保证岗位列表完全可滚动不被遮挡
   - 5.4 [ ] 工具栏响应式：`<md` 将 `Select` 置于标题下方 `w-full mt-3`; `md+` 保持右对齐

6. [x] 测试（R-1, R-2, R-3, R-4, R-5, R-6）
   - 6.1 [ ] 单元：校验函数（阈值前后）与高度控制（短/长文本）
   - 6.2 [ ] 组件：成功/失败流程、键盘行为、loading 状态切换（mock AI/DB）
   - 6.3 [ ] 集成：`/jobs` 渲染后存在底部固定输入条；滚动不被遮挡；提交触发 `onJobAdded` 并调用列表刷新逻辑

7. [x] 质量门禁（R-7, R-8, R-10）
   - 7.1 [ ] ESLint/TS 全绿，中文提示，无敏感日志
   - 7.2 [ ] 令牌一致性复核（按钮、边框、阴影、圆角、间距）

——
实现建议：每个子任务完成后立即运行测试/构建；如新增测试目录与工具已存在则复用现有测试框架组织用例。


