### Inline Job Composer — 需求说明（/jobs 内联悬浮输入区）

#### 简介
在 `src/app/(dashboard)/jobs/page.tsx` 中，用“屏幕底部固定的输入区（Inline Job Composer Bar）”替代点击“添加申请”后弹出对话框的步骤。该输入区以固定定位附着在屏幕底部，不遮挡主体列表滚动；用户可直接粘贴完整岗位描述并一键确认；后台沿用既有 `formatJobListing`（AI 解析）与 `createJob`（写入 DB）流程。输入区支持自适应高度（上限约 6 行 ≈ 144px），超过后内部滚动；同时页面主体在底部预留动态间距以避免被遮挡。遵循 `CLAUDE.md` 与 `DESIGN_TOKENS_USAGE.md` 的设计规范与最小改动原则；不删除旧实现，仅在页面层面停止调用以便回滚。

#### 用户故事
1. 作为求职者，我希望在 `/jobs` 页面直接粘贴岗位描述并一键创建申请，从而减少步骤、快速进入流程。
2. 作为求职者，我希望输入框能随内容自适应高度，超过合理上限后滚动，保证长文本可读与可编辑。
3. 作为求职者，我希望提交后自动打开新建岗位详情并收到中文提示，从而确信操作成功。
4. 作为产品/设计，我希望新组件严格遵循设计令牌与极简风格，且以最小改动接入并可快速回滚。

#### 编号化需求（EARS）
- R-1（呈现/位置）：当用户访问 `/jobs` 时，系统应在屏幕底部呈现一个固定输入区（不随内容滚动消失），其宽度与页面容器对齐（`container max-w-6xl`），包含说明文字、`Textarea`、“确认创建/清空”按钮；采用白底、灰边、圆角与阴影的令牌化样式，并具备顶部轻微渐隐分隔或边框。
- R-1.1（顶部工具栏优化）：当移除“添加申请”按钮后，页面标题右侧操作区应仅保留“阶段筛选 Select”，并进行布局压缩与对齐优化：
  - 在 `md+` 断点保持右对齐，`gap-2` 间距；
  - 在小屏（`<md`）下，`Select` 改为占满行宽，堆叠到标题下方，保持 `w-full` 与合理的 `mt-3`；
  - 不得留下多余空白或占位元素。
 - R-1.2（底部间距与安全区）：固定输入区不得完全贴合屏幕底边，应上浮留出 8–12px 的可视间距；在具有安全区的设备（如 iOS）需额外考虑 `safe-area-inset-bottom`，总预留=视觉间距+安全区；页面主体需增加匹配的底部留白以避免内容被遮挡。
 - R-1.3（响应式断点适配）：固定输入区与顶部工具栏需在三档断点下表现一致、不卡布局：
   - Mobile（`<md`）：
     - 顶部工具栏：`Select` 占满一行置于标题下方（`w-full mt-3`）。
     - 底部输入条：面板 `p-3`，`Textarea` 占满行，操作按钮区域在下一行垂直堆叠或横向等分（以不溢出为准），保证触控目标≥44px。
   - Tablet（`md:... <lg`）：
     - 顶部工具栏：标题左、`Select` 右对齐并保持 `gap-2`。
     - 底部输入条：面板 `md:p-4`，`Textarea` 全宽，按钮横向排列并右对齐。
   - Desktop（`>=lg`）：
     - 容器 `container max-w-6xl` 居中，底栏样式与 `md` 一致，仅保留更舒适的间距与对齐，不引入额外元素。
- R-2（自适应高度）：当用户在 `Textarea` 输入或粘贴文本时，输入区应自适应增高，最大高度约 6 行（≈144px）；当文本高度超过上限时，输入区应启用垂直滚动而非继续增高。
- R-3（键盘交互）：当用户在 `Textarea` 按下 Enter（未按 Shift）时，系统应触发与“确认创建”按钮相同的提交流程；当用户按下 Shift+Enter 时，系统应仅换行并保持自适应高度。
- R-4（输入校验）：当文本长度 < 50 或 > 10000 字符时，系统应拒绝提交并以中文 toast 提示具体原因。
- R-5（创建流程）：当提交有效时，系统应沿用 `formatJobListing(jobDescription,{ model }) → createJob(formattedJob)` 流程创建岗位；成功后应以中文 toast 提示、刷新列表并自动打开新岗位详情。
- R-6（错误处理）：当 AI/网络/鉴权出错时，系统应以中文 toast 告知具体原因（如 API 密钥异常/未登录/网络错误），不得输出敏感日志至控制台。
- R-7（风格规范）：当渲染按钮与输入控件时，系统应遵循 `DESIGN_TOKENS_USAGE.md`：主按钮 `bg-blue-500 text-white`；不使用 hover 改变背景色，仅使用轻量位移/阴影反馈；输入类控件可保留轻量的 focus 可视反馈。
- R-8（可访问性）：当组件渲染时，`label` 应与 `Textarea#id` 关联；错误提示应可被读屏识别（如 `aria-live="polite"`）；键盘应可完成提交；移动端触控目标尺寸应 ≥ 44px。
- R-9（最小改动与回滚）：当集成到页面时，不得删除旧的 `AddJobDialog` 实现；应仅在页面层面注释其调用并保留清晰回滚注释；新增组件文件与页面一处替换为最小改动；为避免遮挡主体列表，应通过动态底部留白（由输入区上报高度或使用固定保守值）保证列表仍可完全可见与可滚动。
- R-10（一致性与安全）：当实现样式时，应使用 Tailwind 语义类与令牌映射，禁止硬编码颜色；日志与提示为中文；不新增服务端接口；沿用 Supabase 认证；满足项目 ESLint/TS 规范。

#### 边界与例外
- 极长文本（>10000 字符）拒绝提交并提示；超长但合法范围内文本在 6 行上限后滚动。
- 网络慢或 AI 延迟：按钮进入 loading 禁用态，避免重复提交；允许取消仅在本期不实现。

- 采用固定定位的底部输入区；粘性吸顶模式不在本次范围（若将来需要，另立需求）。
- 不改动服务端数据模型与现有 AI/DB 流程；不改列表、详情结构。

#### 合规与规范对照
- 设计风格、颜色比例、令牌与交互参考：`CLAUDE.md`、`DESIGN_TOKENS_USAGE.md`
- 代码评审与安全一致性参考：`.cursor/rules/codereview.mdc`


