### Inline Job Composer — 设计文档

#### 概览
在 `/jobs` 页面引入“底部固定输入条”`InlineJobComposer` 客户端组件（fixed 底部，宽度与容器对齐），直接收集岗位描述并触发既有 AI+DB 创建流程。主体岗位列表保持独立滚动，不受影响。遵循最小改动：仅新增 1 个组件文件与页面 1 处调用替换；不删除旧弹窗组件，保留回滚通道。

#### 目标与非目标
- 目标：降低一次点击；提供自适应高度的文本输入；复用 `formatJobListing` 与 `createJob`；保持 UI 风格与令牌一致；中文可用性与可访问性完备。
- 非目标：吸顶（sticky top）模式；服务端 API/数据模型变更；列表与详情的结构改造。

#### 架构与数据流
1) 用户在 `Textarea` 粘贴/输入 → 2) 本地校验（50–10000）→ 3) 读取本地模型 `SnapOffer-default-model` → 4) `formatJobListing(jobDescription,{model})` → 5) `createJob(formattedJob)` → 6) `onJobAdded(newJob)` 回调给父页面 → 7) 父页面刷新列表并自动打开详情 → 8) 中文 toast 成功/失败。

```mermaid
sequenceDiagram
  actor U as User
  participant C as InlineJobComposer
  participant AI as formatJobListing
  participant DB as createJob
  participant P as JobsPage

  U->>C: 粘贴/输入 文本
  C->>C: 本地校验 & 自适应高度
  U->>C: Enter/点击确认
  C->>AI: formatJobListing(text, {model})
  AI-->>C: formattedJob
  C->>DB: createJob(formattedJob)
  DB-->>C: newJob
  C->>P: onJobAdded(newJob)
  P->>P: 刷新列表 & autoOpenJobId=newJob.id
  P-->>U: 打开详情 + 成功 toast
```

#### 组件与接口
- `InlineJobComposer`
  - Props: `onJobAdded?: (job: Job) => void`
  - State: `value: string`, `isFormatting: boolean`, `isCreating: boolean`, `isInvalid: boolean`
  - 事件：
    - `onChange`：更新输入与高度；`Shift+Enter` 换行（`requestAnimationFrame` 后再测量高度）
    - `onSubmit`：校验→AI→DB；统一 loading 状态与错误处理；成功清空并重置
    - `onClear`：清空文本与无效态
  - 结构：底部固定条（适度留白与阴影分隔，包括说明/输入/操作区），与容器等宽；主体容器底部通过 `padding-bottom` 动态/保守留白避免内容被遮挡
  - 接口依赖：`formatJobListing`、`createJob`、`sonner`、`localStorage('SnapOffer-default-model')`

#### 样式与令牌（映射 Tailwind）
- 底栏：`fixed left-0 right-0 z-30` + `bottom-[env(safe-area-inset-bottom)]` 基础对齐，再叠加视觉上浮 `mb-2`（移动端 `mb-3`）；居中容器 `container max-w-6xl mx-auto`；面板 `bg-white border-t border-gray-200 shadow-[0_-4px_12px_rgba(0,0,0,0.06)] rounded-t-lg p-3 md:p-4`
- 文本：标题 `text-black`, 说明 `text-gray-600`
- 输入：`Textarea` 基础 `rounded-xl border-2 bg-background`；本组件附加 `min-h-[84px] max-h-[144px] overflow-y-auto resize-none`；在移动端使用 `text-sm` 与更舒适的 `px-3 py-2.5`
- 主按钮：`bg-blue-500 text-white`（不使用 `hover:bg-*` 改色，保留轻量位移/阴影反馈）
- 次要按钮：`variant="secondary"` 或 `outline`，遵循文档建议

#### 响应式布局细节
- `<md`（mobile）：
  - 顶部工具栏：`Select` 另起一行 `w-full mt-3`
  - 底栏内部：采用两行布局：第一行说明与输入；第二行操作按钮区，`w-full` 横向等分或堆叠（根据可用宽度）；保证按钮触控区域 ≥ 44px
- `md:... <lg`（tablet）：
  - 顶部工具栏：标题与 `Select` 左右对齐
  - 底栏内部：说明/输入/按钮同在一行或 2 列布局（输入占比更大），按钮右对齐
- `>=lg`（desktop）：
  - 使用 `container max-w-6xl` 居中，保持 tablet 的对齐，仅调整间距

#### 自适应高度算法
```text
onChange / after Shift+Enter:
  textarea.style.height = 'auto'
  newHeight = min(textarea.scrollHeight, 144)
  textarea.style.height = `${newHeight}px`
  当 scrollHeight > 144 时，容器保持 `max-h-[144px]` 与 `overflow-y-auto`
```

#### 错误处理与提示
- 校验失败：toast("校验错误") + 具体原因；标记无效态；不提交
- AI 错误：若包含 api key/unauthorized/invalid key 关键字，提示“API 密钥错误”；其余提示“创建失败：{message}”
- DB/鉴权错误：提示“未登录或创建失败”；所有提示为中文；禁用敏感控制台日志

#### 可访问性
- `label` 关联 `for=#job-description`；`aria-invalid` 与 `aria-live="polite"`
- 键盘：Enter 提交、Shift+Enter 换行；按钮 `aria-label` 明确
- 触控目标：按钮最小高宽 ≥ 44px；移动端间距适配

#### 与页面集成
- 在 `JobsPage` 中：
  - 渲染 `InlineJobComposer onJobAdded={handleJobAdded}`（固定底部）
  - 注释 `<AddJobDialog />` 这行调用（不删除文件/导出），在注释处添加“回滚说明”；移除其在工具栏中的占位，使工具栏仅保留 `Select` 并两端对齐
  - 工具栏自适应：`md+` 下右侧 `Select` 与标题区对齐；`<md` 下将 `Select` 置于标题下方一行，`w-full mt-3`

#### 测试策略
- 单元：
  - 校验函数：<50 与 >10000 触发错误
  - 自适应高度：短/长文本分别触发高度≤144px 与滚动
- 组件：
  - 成功路径：mock `formatJobListing`/`createJob`，验证 `onJobAdded` 被调用、按钮 loading 切换、输入清空
  - 失败路径：AI/鉴权/网络错误时 toast 文案
  - 键盘：Enter 提交与 Shift+Enter 换行分支
- 集成：
- `/jobs` 渲染后存在底部固定输入条；列表仍可完整滚动；提交后 `jobListingsRef.refreshJobs()` 与自动打开详情逻辑生效
  - 需验证：输入条与屏幕底边保留 8–12px 视觉上浮间距；在 iOS 等设备上叠加安全区后依然保持统一视觉；主体底部留白与输入条总高度匹配

#### 设计取舍与理由
- 选用页面内卡片而非吸顶/固定：避免遮挡与 z-index 复杂度，保持信息架构稳定；后续可升级为粘性模式
- 仅替换调用点：减少回归风险与合规审查成本，满足“最小改动”与快速回滚

#### 依赖与兼容
- Next.js App Router + React 19（现状）
- 复用内部 `Textarea` 组件与工具函数；不引入新第三方依赖


